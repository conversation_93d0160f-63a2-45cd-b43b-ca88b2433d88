-- Data Migration Script: Safely migrate existing data
-- Date: 2025-08-03
-- Description: Migrate existing data to new schema structure

-- Step 1: Create backup tables
CREATE TABLE `transaction_details_backup` AS SELECT * FROM `transaction_details`;
CREATE TABLE `transactions_backup` AS SELECT * FROM `transactions`;

-- Step 2: Add temporary column for category migration
ALTER TABLE `transaction_details` ADD COLUMN `category_text` TEXT AFTER `category`;

-- Step 3: Copy existing category data to temporary column
UPDATE `transaction_details` SET `category_text` = `category`;

-- Step 4: Update category_id based on pricing table
UPDATE `transaction_details` td 
JOIN `pricing` p ON p.category = td.category_text 
SET td.category = p.id;

-- Step 5: Handle any unmapped categories by creating them in pricing table
INSERT INTO `pricing` (category)
SELECT DISTINCT td.category_text
FROM `transaction_details` td
LEFT JOIN `pricing` p ON p.category = td.category_text
WHERE p.id IS NULL AND td.category_text IS NOT NULL;

-- Step 6: Update remaining unmapped records
UPDATE `transaction_details` td 
JOIN `pricing` p ON p.category = td.category_text 
SET td.category = p.id
WHERE td.category = 0 OR td.category IS NULL;

-- Step 7: Remove temporary column
ALTER TABLE `transaction_details` DROP COLUMN `category_text`;

-- Step 8: Update existing transaction totals using the stored procedure
-- This will populate the new balance tracking columns
DELIMITER //
CREATE PROCEDURE MigrateTransactionTotals()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE trans_id INT;
    DECLARE cur CURSOR FOR SELECT transaction_id FROM transactions;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO trans_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        CALL UpdateTransactionTotals(trans_id);
    END LOOP;
    
    CLOSE cur;
END //
DELIMITER ;

-- Execute the migration
CALL MigrateTransactionTotals();

-- Step 9: Clean up migration procedure
DROP PROCEDURE MigrateTransactionTotals;

-- Step 10: Verify data integrity
SELECT 
    'Transactions with balance issues' as check_type,
    COUNT(*) as count
FROM transactions 
WHERE ABS(balance - (total_amount - amount_paid)) > 0.01

UNION ALL

SELECT 
    'Transaction details without valid category' as check_type,
    COUNT(*) as count
FROM transaction_details td
LEFT JOIN pricing p ON td.category = p.id
WHERE p.id IS NULL

UNION ALL

SELECT 
    'Payments without valid transaction' as check_type,
    COUNT(*) as count
FROM payments pay
LEFT JOIN transactions t ON pay.transaction_id = t.transaction_id
WHERE t.transaction_id IS NULL;

-- Step 11: Create summary report
SELECT 
    'Migration Summary' as report_type,
    'Total Transactions' as metric,
    COUNT(*) as value
FROM transactions

UNION ALL

SELECT 
    'Migration Summary' as report_type,
    'Transactions with Balance' as metric,
    COUNT(*) as value
FROM transactions
WHERE balance > 0

UNION ALL

SELECT 
    'Migration Summary' as report_type,
    'Paid Transactions' as metric,
    COUNT(*) as value
FROM transactions
WHERE status = 'PAID'

UNION ALL

SELECT 
    'Migration Summary' as report_type,
    'Partial Payments' as metric,
    COUNT(*) as value
FROM transactions
WHERE status = 'PARTIAL';
