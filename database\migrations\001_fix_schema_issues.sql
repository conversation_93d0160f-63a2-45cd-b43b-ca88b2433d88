-- Migration Script: Fix Database Schema Issues
-- Date: 2025-08-03
-- Description: Fix table names, add missing columns, and improve data integrity

-- 1. Fix table name typo: princing_detail -> pricing_detail
RENAME TABLE `princing_detail` TO `pricing_detail`;

-- 2. Fix column name typo in addons table
ALTER TABLE `addons` CHANGE COLUMN `adons_id` `addons_id` INT(10) NOT NULL AUTO_INCREMENT;

-- 3. Add balance column to transactions table for better tracking
ALTER TABLE `transactions` 
ADD COLUMN `total_amount` DECIMAL(10,2) DEFAULT 0.00 AFTER `transaction_date`,
ADD COLUMN `amount_paid` DECIMAL(10,2) DEFAULT 0.00 AFTER `total_amount`,
ADD COLUMN `balance` DECIMAL(10,2) DEFAULT 0.00 AFTER `amount_paid`,
ADD COLUMN `status` ENUM('PENDING', 'PARTIAL', 'PAID', 'CANCELLED') DEFAULT 'PENDING' AFTER `balance`;

-- 4. Modify transaction_details to use category ID instead of TEXT
ALTER TABLE `transaction_details` 
CHANGE COLUMN `category` `category_id` INT(10) NOT NULL;

-- 5. Add foreign key constraints for better data integrity
ALTER TABLE `transaction_details` 
ADD CONSTRAINT `fk_transaction_details_category` 
FOREIGN KEY (`category_id`) REFERENCES `pricing` (`id`) ON UPDATE CASCADE ON DELETE RESTRICT;

ALTER TABLE `addons` 
ADD CONSTRAINT `fk_addons_transaction` 
FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`transaction_id`) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `pricing_detail` 
ADD CONSTRAINT `fk_pricing_detail_category` 
FOREIGN KEY (`cat_id`) REFERENCES `pricing` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

-- 6. Add indexes for better performance
CREATE INDEX `idx_transactions_customer_date` ON `transactions` (`customer_id`, `transaction_date`);
CREATE INDEX `idx_payments_transaction_date` ON `payments` (`transaction_id`, `created_at`);
CREATE INDEX `idx_loyalty_points_customer_status` ON `loyalty_points` (`customer_id`, `status`);
CREATE INDEX `idx_transaction_details_transaction` ON `transaction_details` (`transaction_id`);
CREATE INDEX `idx_addons_transaction` ON `addons` (`transaction_id`);

-- 7. Create a view for easier balance calculations
CREATE VIEW `transaction_summary` AS
SELECT 
    t.transaction_id,
    t.customer_id,
    t.transaction_date,
    t.total_amount,
    t.amount_paid,
    t.balance,
    t.status,
    c.full_name,
    c.phone_number,
    COALESCE(SUM(p.amount_paid), 0) as total_payments,
    (t.total_amount - COALESCE(SUM(p.amount_paid), 0)) as calculated_balance
FROM transactions t
LEFT JOIN customers c ON t.customer_id = c.customer_id
LEFT JOIN payments p ON t.transaction_id = p.transaction_id
GROUP BY t.transaction_id;

-- 8. Create stored procedure for updating transaction totals
DELIMITER //
CREATE PROCEDURE UpdateTransactionTotals(IN trans_id INT)
BEGIN
    DECLARE total_items DECIMAL(10,2) DEFAULT 0;
    DECLARE total_addons DECIMAL(10,2) DEFAULT 0;
    DECLARE total_payments DECIMAL(10,2) DEFAULT 0;
    DECLARE new_balance DECIMAL(10,2) DEFAULT 0;
    DECLARE new_status VARCHAR(20) DEFAULT 'PENDING';
    
    -- Calculate total from transaction details
    SELECT COALESCE(SUM(subtotal), 0) INTO total_items
    FROM transaction_details 
    WHERE transaction_id = trans_id;
    
    -- Calculate total from addons
    SELECT COALESCE(SUM(trans_subtotal), 0) INTO total_addons
    FROM addons 
    WHERE transaction_id = trans_id;
    
    -- Calculate total payments
    SELECT COALESCE(SUM(amount_paid), 0) INTO total_payments
    FROM payments 
    WHERE transaction_id = trans_id;
    
    -- Calculate balance and status
    SET new_balance = (total_items + total_addons) - total_payments;
    
    IF new_balance <= 0 THEN
        SET new_status = 'PAID';
    ELSEIF total_payments > 0 THEN
        SET new_status = 'PARTIAL';
    ELSE
        SET new_status = 'PENDING';
    END IF;
    
    -- Update transaction
    UPDATE transactions 
    SET 
        total_amount = total_items + total_addons,
        amount_paid = total_payments,
        balance = new_balance,
        status = new_status
    WHERE transaction_id = trans_id;
END //
DELIMITER ;

-- 9. Create trigger to automatically update transaction totals
DELIMITER //
CREATE TRIGGER tr_transaction_details_update 
AFTER INSERT ON transaction_details
FOR EACH ROW
BEGIN
    CALL UpdateTransactionTotals(NEW.transaction_id);
END //

CREATE TRIGGER tr_transaction_details_update_modify
AFTER UPDATE ON transaction_details
FOR EACH ROW
BEGIN
    CALL UpdateTransactionTotals(NEW.transaction_id);
END //

CREATE TRIGGER tr_addons_update
AFTER INSERT ON addons
FOR EACH ROW
BEGIN
    CALL UpdateTransactionTotals(NEW.transaction_id);
END //

CREATE TRIGGER tr_payments_update
AFTER INSERT ON payments
FOR EACH ROW
BEGIN
    CALL UpdateTransactionTotals(NEW.transaction_id);
END //
DELIMITER ;

-- 10. Update existing data to fix category references
-- This will need to be run after ensuring pricing categories exist
-- UPDATE transaction_details td 
-- JOIN pricing p ON p.category = td.category 
-- SET td.category_id = p.id;
