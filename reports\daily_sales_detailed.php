<?php
include_once '../config/database.php';

// Get sales records for selected date
$selected_date = isset($_GET['selected_date']) ? $_GET['selected_date'] : '';
$sales_records = [];
$total_sales = 0;

if ($selected_date) {
    // Get sales records grouped by customer with payment method
    $sql = "SELECT 
                c.full_name,
                td.transaction_id,
                td.category,
                td.weight,
                CASE 
                    WHEN p.method = 'loyalty' THEN -td.subtotal 
                    ELSE td.subtotal 
                END as subtotal,
                t.transaction_date,
                p.method as payment_method
            FROM transaction_details td
            INNER JOIN transactions t ON td.transaction_id = t.transaction_id
            INNER JOIN customers c ON t.customer_id = c.customer_id
            LEFT JOIN payments p ON td.transaction_id = p.transaction_id
            WHERE DATE(t.transaction_date) = ?
            ORDER BY c.full_name, td.transaction_id";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$selected_date]);
    $sales_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate total sales (excluding loyalty payments)
    $sql = "SELECT SUM(
                CASE 
                    WHEN p.method = 'loyalty' THEN 0
                    ELSE td.subtotal 
                END
            ) as total 
            FROM transaction_details td
            INNER JOIN transactions t ON td.transaction_id = t.transaction_id
            LEFT JOIN payments p ON td.transaction_id = p.transaction_id
            WHERE DATE(t.transaction_date) = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$selected_date]);
    $total_sales = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Daily Sales Detailed Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h2 class="card-title h4 mb-4">
                    <i class="fa-solid fa-file-invoice-dollar me-2"></i>Daily Sales Detailed Report
                </h2>
                
                <form method="GET" class="needs-validation" novalidate>
                    <div class="row g-3 align-items-end">
                        <div class="col-md-10">
                            <label class="form-label small text-muted">Select Date</label>
                            <input type="date" 
                                   name="selected_date" 
                                   class="form-control form-control-lg shadow-sm" 
                                   value="<?php echo $selected_date; ?>" 
                                   required>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary btn-lg w-100 mb-2 shadow-sm">
                                <i class="fa-solid fa-search me-2"></i>View Sales
                            </button>
                            <?php
                            $filename = 'daily_sales_' . date('Y-m-d', strtotime($selected_date));
                            ?>
                            <a href="generate_daily_sales_pdf.php?selected_date=<?php echo $selected_date; ?>&filename=<?php echo $filename; ?>" 
                                class="btn btn-outline-danger btn-lg w-100 mb-2 shadow-sm"
                                target="_blank">
                                 <i class="fa-solid fa-file-pdf me-2"></i>Export PDF
                            </a>
                            <a href="../pages/reports.php" class="btn btn-secondary btn-lg w-100 shadow-sm">
                                <i class="fa-solid fa-arrow-left me-2"></i>Back
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped">
            <thead>
                <tr>
                <th>Customer Name</th>
                <th>Transaction ID</th>
                <th>Category</th>
                <th>Weight</th>
                <th>Subtotal</th>
                </tr>
            </thead>
            <tbody>
                <?php 
                $current_customer = '';
                $customer_total = 0;
                
                foreach ($sales_records as $record): 
                if ($current_customer != $record['full_name']) {
                    if ($current_customer != '') {
                    // Print customer subtotal
                    echo '<tr class="table-info">
                        <td colspan="4" class="text-end">Customer Total:</td>
                        <td>₱' . number_format(abs($customer_total), 2) . '</td>
                    </tr>';
                    }
                    $current_customer = $record['full_name'];
                    $customer_total = 0;
                }
                $customer_total += abs($record['subtotal']); // Use absolute value to include all transactions
                ?>
                <tr>
                    <td><?php echo htmlspecialchars($record['full_name']); ?></td>
                    <td><?php echo htmlspecialchars($record['transaction_id']); ?></td>
                    <td><?php echo htmlspecialchars($record['category']); ?></td>
                    <td><?php echo htmlspecialchars($record['weight']); ?></td>
                    <td>₱<?php echo number_format($record['subtotal'], 2); ?></td>
                </tr>
                <?php endforeach; 
                
                if ($current_customer != '') {
                // Print last customer subtotal
                echo '<tr class="table-info">
                    <td colspan="4" class="text-end">Customer Total:</td>
                    <td>₱' . number_format(abs($customer_total), 2) . '</td>
                </tr>';
                }
                ?>
                
                <?php if (!empty($sales_records)): ?>
                <tr class="table-primary fw-bold">
                    <td colspan="4" class="text-end">Total Sales:</td>
                    <td>₱<?php echo number_format($total_sales, 2); ?></td>
                </tr>
                <?php endif; ?>

                <?php if (!empty($sales_records)): 
                // Calculate total sales without deducting loyalty
                $sql = "SELECT SUM(td.subtotal) as total 
                    FROM transaction_details td
                    INNER JOIN transactions t ON td.transaction_id = t.transaction_id
                    WHERE DATE(t.transaction_date) = ?";
                
                $stmt = $conn->prepare($sql);
                $stmt->execute([$selected_date]);
                $total_without_loyalty = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
                ?>
                <tr class="table-success fw-bold">
                    <td colspan="4" class="text-end">Total Sales (Without Loyalty Deduction):</td>
                    <td>₱<?php echo number_format($total_without_loyalty, 2); ?></td>
                </tr>
                <?php endif; ?>
            </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
