<?php
session_start();
include_once '../config/database.php';

// Check if employee is logged in
if (!isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $employee_id = $_SESSION['employee_id'];
    $date = date('Y-m-d');
    $time = date('H:i:s');
    
    if (isset($_POST['login'])) {
        $sql = "INSERT INTO attendance (employee_id, date, time, status) VALUES (?, ?, ?, 'IN')";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$employee_id, $date, $time]);
        $message = "Login recorded successfully!";
    }
    
    if (isset($_POST['logout'])) {
        $sql = "INSERT INTO attendance (employee_id, date, time, status) VALUES (?, ?, ?, 'OUT')";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$employee_id, $date, $time]);
        $message = "Logout recorded successfully!";
    }
}

// Fetch employee details
$sql = "SELECT full_name FROM employees WHERE employee_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$_SESSION['employee_id']]);
$employee = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<!-- Previous PHP code remains the same -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Biometric Attendance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { 
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .container { max-width: 800px; }
        .attendance-card {
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 20px;
            padding: 2.5rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .btn {
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .welcome-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .time-display {
            font-size: 2.5rem;
            font-weight: 300;
            color: #2c3e50;
        }
        .date-display {
            font-size: 1.2rem;
            color: #6c757d;
        }
        .btn-action {
            padding: 1rem 2.5rem;
            font-size: 1.1rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="attendance-card">
            <h2 class="text-center mb-4" style="color: #2c3e50; font-weight: 600;">
                <i class="fas fa-clock me-2"></i>Attendance System
            </h2>
            
            <?php if (isset($message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert" 
                     style="border-radius: 10px; border: none;">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="welcome-section text-center">
                <h4 style="color: #2c3e50; font-weight: 600;">
                    <i class="fas fa-user-circle me-2"></i>
                    <?php echo htmlspecialchars($employee['full_name']); ?>
                </h4>
                <div class="mt-4">
                    <p class="date-display mb-2">
                        <i class="fas fa-calendar-alt me-2"></i><?php echo date('F d, Y'); ?>
                    </p>
                    <p class="time-display mb-0" id="current-time"></p>
                </div>
            </div>

            <form method="POST" class="d-flex justify-content-center gap-4 mb-4">
                <button type="submit" name="login" class="btn btn-success btn-action">
                    <i class="fas fa-sign-in-alt me-2"></i> Clock In
                </button>
                <button type="submit" name="logout" class="btn btn-danger btn-action">
                    <i class="fas fa-sign-out-alt me-2"></i> Clock Out
                </button>
            </form>

            <div class="text-center mt-4">
                <a href="../index.php" class="btn btn-secondary btn-action">
                    <i class="fas fa-home me-2"></i> Home
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateTime() {
            const timeElement = document.getElementById('current-time');
            const now = new Date();
            timeElement.textContent = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });
        }
        
        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html>
