<?php
session_start();

include 'config/database.php'; // Include database configuration

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // Create database connection
        $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Get user input
        $username = $_POST['username'];
        $submitted_password = $_POST['password'];

        // Prepare SQL statement with JOIN to get employee information
        $stmt = $conn->prepare("
            SELECT u.*, e.employee_id as employee_id, e.full_name, u.type, u.user_status 
            FROM users u 
            INNER JOIN employees e ON u.employee_id = e.employee_id 
            WHERE u.username = ? 
        ");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // Check if user exists and verify password
        if ($user && password_verify($submitted_password, $user['password'])) {
            // Check if user account is active
            if ($user['user_status'] === 'Inactive') {
                $_SESSION['error'] = "Your account is inactive. Please contact the administrator.";
                header("Location: login.php");
                exit();
            }
            
            // Login successful
            $_SESSION['username'] = $username;
            $_SESSION['employee_id'] = $user['employee_id'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['type'] = $user['type'];
            header("Location: index.php"); // Redirect to dashboard
            exit();
        } else {
            // Login failed
            $_SESSION['error'] = "Invalid username or password! Try again, Hero! 🦸‍♂️";
            header("Location: login.php");
            exit();
        }

    } catch(PDOException $e) {
        $_SESSION['error'] = "Connection failed: " . $e->getMessage();
        header("Location: login.php");
        exit();
    }
} else {
    // If someone tries to access this file directly
    header("Location: login.php");
    exit();
}
?>
