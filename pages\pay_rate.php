<?php
// Database connection parameters
include_once '../config/database.php'; 

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $employee_id = $_POST['employee_id'];
        $daily_rate = $_POST['daily_rate'];
        
        $sql = "INSERT INTO pay (employee_id, daily_rate) VALUES (?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$employee_id, $daily_rate]);
    }

    if (isset($_POST['update']) && isset($_POST['pay_id'])) {
        $pay_id = $_POST['pay_id'];
        $daily_rate = $_POST['daily_rate'];
        
        $sql = "UPDATE pay SET daily_rate=? WHERE pay_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$daily_rate, $pay_id]);
    }
}

// Get employees list
$sql = "SELECT employee_id, full_name FROM employees";
$stmt = $conn->prepare($sql);
$stmt->execute();
$employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';

$sql = "SELECT p.*, e.full_name 
        FROM pay p 
        JOIN employees e ON p.employee_id = e.employee_id 
        WHERE e.full_name LIKE ?";
$search_term = "%$search%";
$stmt = $conn->prepare($sql);
$stmt->execute([$search_term]);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Pay Rate Management</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; }
        .table thead th { background-color: #0d6efd; color: white; }
        .input-group { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Pay Rate Management</h2>
        
        <!-- Search Form -->
        <form method="GET" class="mb-4">
            <div class="row">
                <div class="col-md-10">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                        <input type="text" name="search" class="form-control" placeholder="Search employee..." value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100"><i class="fa-solid fa-search"></i> Search</button>
                </div>
            </div>
        </form>

        <!-- Add/Edit Pay Rate Form -->
        <form method="POST" class="mb-4">
            <input type="hidden" name="pay_id" id="pay_id">
            <div class="row mb-3">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-user"></i></span>
                        <select name="employee_id" class="form-control" required>
                            <option value="">Select Employee</option>
                            <?php foreach($employees as $employee): ?>
                                <option value="<?php echo $employee['employee_id']; ?>">
                                    <?php echo htmlspecialchars($employee['full_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-money-bill"></i></span>
                        <input type="number" step="0.01" name="daily_rate" class="form-control" placeholder="Daily Rate" required>
                    </div>
                </div>
                <div class="col">
                    <div class="btn-group" role="group">
                        <button type="submit" name="save" class="btn btn-success"><i class="fa-solid fa-plus"></i> Save New</button>
                        <button type="submit" name="update" class="btn btn-warning"><i class="fa-solid fa-pen"></i> Update</button>
                        <a href="../reports/payroll_section.php" class="btn btn-secondary"><i class="fa-solid fa-home"></i> Payroll Report Home</a>
                    </div>
                </div>
            </div>
        </form>

        <!-- Pay Rates Table -->
        <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-striped">
                <thead style="position: sticky; top: 0; background: white;">
                    <tr>
                        <th><i class="fa-solid fa-id-card"></i> ID</th>
                        <th><i class="fa-solid fa-user"></i> Employee</th>
                        <th><i class="fa-solid fa-money-bill"></i> Daily Rate</th>
                        <th><i class="fa-solid fa-gear"></i> Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($result as $row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['pay_id']); ?></td>
                        <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($row['daily_rate']); ?></td>
                        <td>
                            <button onclick="editPayRate(<?php echo htmlspecialchars(json_encode($row)); ?>)" 
                                    class="btn btn-sm btn-primary">Edit</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function editPayRate(pay) {
        document.querySelector('[name="pay_id"]').value = pay.pay_id;
        document.querySelector('[name="employee_id"]').value = pay.employee_id;
        document.querySelector('[name="daily_rate"]').value = pay.daily_rate;
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
