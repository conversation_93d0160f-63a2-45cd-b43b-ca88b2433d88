# Database Migration Instructions

## Quick Fix for Current Error

The error you're seeing is because the database schema hasn't been updated yet. The code is looking for `category_id` column which doesn't exist in the current schema.

### Option 1: Run Automatic Migration (Recommended)

1. **Navigate to the migration script in your browser:**
   ```
   http://localhost/lmsv2/database/check_and_migrate.php
   ```

2. **The script will automatically:**
   - Check your current database schema
   - Apply necessary migrations safely
   - Show you what changes were made
   - Preserve all your existing data

3. **After migration, refresh your transaction details page**

### Option 2: Manual Database Updates

If you prefer to run SQL manually, execute these commands in your database:

```sql
-- 1. Fix table name typo (if exists)
RENAME TABLE `princing_detail` TO `pricing_detail`;

-- 2. Fix column name typo in addons table
ALTER TABLE `addons` CHANGE COLUMN `adons_id` `addons_id` INT(10) NOT NULL AUTO_INCREMENT;

-- 3. Add balance tracking columns to transactions
ALTER TABLE `transactions` 
ADD COLUMN `total_amount` DECIMAL(10,2) DEFAULT 0.00 AFTER `transaction_date`,
ADD COLUMN `amount_paid` DECIMAL(10,2) DEFAULT 0.00 AFTER `total_amount`,
ADD COLUMN `balance` DECIMAL(10,2) DEFAULT 0.00 AFTER `amount_paid`,
ADD COLUMN `status` ENUM('PENDING', 'PARTIAL', 'PAID', 'CANCELLED') DEFAULT 'PENDING' AFTER `balance`;

-- 4. Add category_id column to transaction_details
ALTER TABLE `transaction_details` ADD COLUMN `category_id` INT(10) AFTER `transaction_id`;

-- 5. Migrate existing category data
UPDATE transaction_details td 
JOIN pricing p ON p.category = td.category 
SET td.category_id = p.id;

-- 6. Add foreign key constraint
ALTER TABLE `transaction_details` 
ADD CONSTRAINT `fk_transaction_details_category` 
FOREIGN KEY (`category_id`) REFERENCES `pricing` (`id`) ON UPDATE CASCADE ON DELETE RESTRICT;

-- 7. Add performance indexes
CREATE INDEX `idx_transactions_customer_date` ON `transactions` (`customer_id`, `transaction_date`);
CREATE INDEX `idx_payments_transaction_date` ON `payments` (`transaction_id`, `created_at`);
CREATE INDEX `idx_loyalty_points_customer_status` ON `loyalty_points` (`customer_id`, `status`);
CREATE INDEX `idx_transaction_details_transaction` ON `transaction_details` (`transaction_id`);
CREATE INDEX `idx_addons_transaction` ON `addons` (`transaction_id`);

-- 8. Update existing transaction totals
UPDATE transactions t
SET 
    total_amount = (
        SELECT COALESCE(SUM(td.subtotal), 0) + COALESCE(SUM(a.trans_subtotal), 0)
        FROM transaction_details td
        LEFT JOIN addons a ON td.transaction_id = a.transaction_id
        WHERE td.transaction_id = t.transaction_id
    ),
    amount_paid = (
        SELECT COALESCE(SUM(p.amount_paid), 0)
        FROM payments p
        WHERE p.transaction_id = t.transaction_id
    );

-- 9. Update balance and status
UPDATE transactions SET balance = total_amount - amount_paid;
UPDATE transactions SET status = CASE 
    WHEN balance <= 0 THEN 'PAID'
    WHEN amount_paid > 0 THEN 'PARTIAL'
    ELSE 'PENDING'
END;
```

## What the Migration Does

### 1. **Fixes Database Schema Issues**
- Corrects table name typo: `princing_detail` → `pricing_detail`
- Fixes column name typo: `adons_id` → `addons_id`
- Adds proper balance tracking columns

### 2. **Improves Data Structure**
- Adds `category_id` column for proper foreign key relationships
- Migrates existing category data safely
- Adds foreign key constraints for data integrity

### 3. **Enhances Performance**
- Adds strategic indexes for faster queries
- Optimizes frequently used query patterns

### 4. **Updates Existing Data**
- Calculates and populates balance tracking for existing transactions
- Updates transaction statuses based on payment history
- Preserves all existing data during migration

## Backward Compatibility

The improved `transaction_details.php` file is designed to work with both:
- **Old schema** (current database structure)
- **New schema** (after migration)

This means:
- ✅ Your system will work immediately without migration
- ✅ You can run the migration when convenient
- ✅ No data loss during the transition
- ✅ Enhanced features available after migration

## Benefits After Migration

### 1. **Better Performance**
- Faster queries with proper indexes
- Optimized balance calculations
- Reduced database load

### 2. **Enhanced Features**
- Real-time balance tracking
- Automatic transaction status updates
- Improved loyalty points management
- Better error handling

### 3. **Data Integrity**
- Foreign key constraints prevent invalid data
- Automatic balance reconciliation
- Consistent data relationships

### 4. **Easier Maintenance**
- Cleaner code structure
- Better error reporting
- Simplified debugging

## Troubleshooting

### If Migration Fails:
1. **Check database permissions** - Ensure your database user has ALTER, CREATE, and UPDATE privileges
2. **Backup first** - Always backup your database before running migrations
3. **Check for conflicts** - Ensure no other processes are using the database during migration

### If You See Errors After Migration:
1. **Clear browser cache** - Force refresh the page (Ctrl+F5)
2. **Check error logs** - Look in your PHP error logs for specific issues
3. **Verify migration** - Run the migration script again to ensure all changes were applied

## Support

If you encounter any issues:
1. Check the migration script output for specific error messages
2. Verify your database connection settings
3. Ensure your database user has sufficient privileges
4. Contact support with the specific error message and your database structure

---

**Note:** The migration is designed to be safe and preserve all your existing data. However, it's always recommended to backup your database before making structural changes.
