<?php
/**
 * Quick Schema Fix for Payment Processing Issues
 * This script adds the missing columns to the transactions table if they don't exist
 */

require_once 'config/database.php';

echo "<h2>Quick Schema Fix for Payment Processing</h2>";

try {
    // Check if the required columns exist
    $columns_to_add = [
        'total_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
        'amount_paid' => 'DECIMAL(10,2) DEFAULT 0.00', 
        'balance' => 'DECIMAL(10,2) DEFAULT 0.00',
        'status' => "ENUM('PENDING','PARTIAL','PAID','CANCELLED') DEFAULT 'PENDING'"
    ];
    
    $columns_added = [];
    $columns_exist = [];
    
    foreach ($columns_to_add as $column_name => $column_definition) {
        // Check if column exists
        $check_sql = "SHOW COLUMNS FROM transactions LIKE '$column_name'";
        $result = $conn->query($check_sql);
        
        if ($result->rowCount() == 0) {
            // Column doesn't exist, add it
            $alter_sql = "ALTER TABLE transactions ADD COLUMN $column_name $column_definition";
            $conn->exec($alter_sql);
            $columns_added[] = $column_name;
            echo "<p>✅ Added column: <strong>$column_name</strong></p>";
        } else {
            $columns_exist[] = $column_name;
            echo "<p>ℹ️ Column already exists: <strong>$column_name</strong></p>";
        }
    }
    
    // If we added columns, update existing transactions
    if (!empty($columns_added)) {
        echo "<h3>Updating Existing Transaction Data</h3>";
        
        // Get all transactions that need updating
        $transactions_sql = "SELECT transaction_id FROM transactions WHERE total_amount = 0 OR total_amount IS NULL";
        $stmt = $conn->prepare($transactions_sql);
        $stmt->execute();
        $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $updated_count = 0;
        
        foreach ($transactions as $transaction) {
            $transaction_id = $transaction['transaction_id'];
            
            // Calculate total from transaction details
            $details_sql = "SELECT COALESCE(SUM(subtotal), 0) as total_items
                           FROM transaction_details
                           WHERE transaction_id = ?";
            $stmt = $conn->prepare($details_sql);
            $stmt->execute([$transaction_id]);
            $total_items = $stmt->fetch(PDO::FETCH_ASSOC)['total_items'] ?? 0;
            
            // Calculate total from addons
            $addons_sql = "SELECT COALESCE(SUM(trans_subtotal), 0) as total_addons
                          FROM addons
                          WHERE transaction_id = ?";
            $stmt = $conn->prepare($addons_sql);
            $stmt->execute([$transaction_id]);
            $total_addons = $stmt->fetch(PDO::FETCH_ASSOC)['total_addons'] ?? 0;
            
            // Calculate total payments
            $payments_sql = "SELECT COALESCE(SUM(amount_paid), 0) as total_payments
                            FROM payments
                            WHERE transaction_id = ?";
            $stmt = $conn->prepare($payments_sql);
            $stmt->execute([$transaction_id]);
            $total_payments = $stmt->fetch(PDO::FETCH_ASSOC)['total_payments'] ?? 0;
            
            // Calculate totals and status
            $total_amount = $total_items + $total_addons;
            $balance = $total_amount - $total_payments;
            
            // Determine status
            if ($balance <= 0 && $total_payments > 0) {
                $status = 'PAID';
            } elseif ($total_payments > 0) {
                $status = 'PARTIAL';
            } else {
                $status = 'PENDING';
            }
            
            // Update transaction
            $update_sql = "UPDATE transactions
                          SET total_amount = ?,
                              amount_paid = ?,
                              balance = ?,
                              status = ?
                          WHERE transaction_id = ?";
            $stmt = $conn->prepare($update_sql);
            $stmt->execute([$total_amount, $total_payments, $balance, $status, $transaction_id]);
            
            $updated_count++;
        }
        
        echo "<p>✅ Updated <strong>$updated_count</strong> transactions with calculated totals</p>";
    }
    
    // Add some useful indexes if they don't exist
    echo "<h3>Adding Performance Indexes</h3>";
    
    $indexes_to_add = [
        'idx_transactions_status' => 'CREATE INDEX idx_transactions_status ON transactions(status)',
        'idx_transactions_customer_date' => 'CREATE INDEX idx_transactions_customer_date ON transactions(customer_id, transaction_date)',
        'idx_payments_transaction' => 'CREATE INDEX idx_payments_transaction ON payments(transaction_id)',
        'idx_transaction_details_transaction' => 'CREATE INDEX idx_transaction_details_transaction ON transaction_details(transaction_id)'
    ];
    
    foreach ($indexes_to_add as $index_name => $create_sql) {
        try {
            // Check if index exists
            $check_index = $conn->query("SHOW INDEX FROM transactions WHERE Key_name = '$index_name'");
            if ($check_index->rowCount() == 0) {
                $conn->exec($create_sql);
                echo "<p>✅ Added index: <strong>$index_name</strong></p>";
            } else {
                echo "<p>ℹ️ Index already exists: <strong>$index_name</strong></p>";
            }
        } catch (Exception $e) {
            // Index might already exist or there might be a different issue
            echo "<p>⚠️ Could not add index $index_name: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>Schema Fix Complete!</h3>";
    
    if (!empty($columns_added)) {
        echo "<p>🎉 <strong>Success!</strong> Added " . count($columns_added) . " missing columns and updated existing data.</p>";
        echo "<p>Your payment processing should now work correctly.</p>";
    } else {
        echo "<p>ℹ️ All required columns already exist. The issue might be elsewhere.</p>";
        echo "<p>Try using the debug script to identify the specific problem:</p>";
        echo "<p><a href='debug_payment_issue.php?transaction_id=1'>Debug Payment Issue</a></p>";
    }
    
    echo "<hr>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>Test payment processing on a transaction</li>";
    echo "<li>If issues persist, use the debug script to identify the problem</li>";
    echo "<li>Check the browser console and server error logs for additional details</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and permissions.</p>";
}

echo "<hr>";
echo "<p><a href='pages/transactions.php'>← Back to Transactions</a></p>";
?>
