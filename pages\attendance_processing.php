<?php
include_once '../config/database.php';

// Get list of employees
$sql = "SELECT employee_id, full_name FROM employees ORDER BY full_name";
$stmt = $conn->prepare($sql);
$stmt->execute();
$employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get selected employee and date range
$selected_employee = isset($_GET['employee_id']) ? $_GET['employee_id'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$attendance_records = [];
$grouped_records = [];
$daily_hours = [];

if ($selected_employee && $date_from && $date_to) {
    // Get attendance records for selected employee and date range
    $sql = "SELECT 
                attendance_id,
                employee_id,
                date,
                time,
                status,
                remarks
            FROM attendance
            WHERE DATE(date) BETWEEN ? AND ? AND employee_id = ?
            ORDER BY date, time";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$date_from, $date_to, $selected_employee]);
    $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Group records by date and calculate worked hours
    foreach ($attendance_records as $record) {
        $date = date('Y-m-d', strtotime($record['date']));
        $grouped_records[$date][] = $record;
        
        // Calculate worked hours for each day
        if (count($grouped_records[$date]) >= 2) {
            $first_time = strtotime($grouped_records[$date][0]['date'] . ' ' . $grouped_records[$date][0]['time']);
            $last_time = strtotime(end($grouped_records[$date])['date'] . ' ' . end($grouped_records[$date])['time']);
            $daily_hours[$date] = round(($last_time - $first_time) / 3600, 2); // Convert to hours
        }
    }
}

// Handle form submission for saving records
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_records'])) {
        $success = true; // Flag to track if all operations are successful
        
        foreach ($grouped_records as $date => $records) {
            if (count($records) >= 2) {
                $first_time = strtotime($records[0]['date'] . ' ' . $records[0]['time']);
                $last_time = strtotime(end($records)['date'] . ' ' . end($records)['time']);
                
                // Get overtime hours and minutes from form input
                $overtime_hours = isset($_POST['overtime_hours_' . $date]) ? intval($_POST['overtime_hours_' . $date]) : 0;
                $overtime_minutes = isset($_POST['overtime_minutes_' . $date]) ? intval($_POST['overtime_minutes_' . $date]) : 0;
                
                // Convert overtime to decimal hours
                $overtime_total = $overtime_hours + ($overtime_minutes / 60);
                
                // Calculate regular hours
                $regular_hours = round(($last_time - $first_time) / 3600, 2);
                
                // Calculate total hours (regular + overtime)
                $total_hours = min($regular_hours, 8) + $overtime_total;
                
                // Format times
                $time_in = date('H:i:s', $first_time);
                $time_out = date('H:i:s', $last_time);

                try {
                    // Check if record exists
                    $check_sql = "SELECT COUNT(*) FROM daily_attendance_summary 
                                WHERE employee_id = ? AND date = ?";
                    $check_stmt = $conn->prepare($check_sql);
                    $check_stmt->execute([$selected_employee, $date]);
                    $exists = $check_stmt->fetchColumn();

                    if ($exists) {
                        // Update existing record
                        $update_sql = "UPDATE daily_attendance_summary 
                                    SET time_in = ?, 
                                        time_out = ?, 
                                        overtime_hours = ?,
                                        total_hours = ?
                                    WHERE employee_id = ? AND date = ?";
                        $update_stmt = $conn->prepare($update_sql);
                        $update_stmt->execute([
                            $time_in, 
                            $time_out, 
                            $overtime_total,
                            $total_hours,
                            $selected_employee, 
                            $date
                        ]);
                    } else {
                        // Insert new record
                        $insert_sql = "INSERT INTO daily_attendance_summary 
                                    (employee_id, date, time_in, time_out, overtime_hours, total_hours)
                                    VALUES (?, ?, ?, ?, ?, ?)";
                        $insert_stmt = $conn->prepare($insert_sql);
                        $insert_stmt->execute([
                            $selected_employee, 
                            $date, 
                            $time_in, 
                            $time_out,
                            $overtime_total,
                            $total_hours
                        ]);
                    }
                } catch (PDOException $e) {
                    $success = false;
                    break;
                }
            }
        }
        
        // Redirect to prevent form resubmission
        header("Location: " . $_SERVER['PHP_SELF'] . "?employee_id=" . $selected_employee . "&date_from=" . $date_from . "&date_to=" . $date_to);
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Records</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2C3E50;
            --accent-color: #3498DB;
            --hover-color: #2980B9;
            --text-color: #ECF0F1;
            --background-color: #1A1A1A;
            --card-background: #2C2C2C;
            --border-color: #34495E;
        }
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--background-color);
            margin: 0;
            padding: 1.5rem;
            color: var(--text-color);
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto;
            background: var(--card-background);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.3);
        }
        .selection-form {
            background: var(--card-background);
            padding: 1rem;
            border-radius: 15px;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 15px rgba(0,0,0,0.2);
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 1.5rem;
            transition: all 0.3s;
        }
        .selection-form:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.4);
        }
        select, input[type="date"] {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            background-color: var(--background-color);
            color: var(--text-color);
            border-radius: 10px;
            font-size: 0.9rem;
            transition: all 0.2s;
        }
        select:focus, input[type="date"]:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        button {
            background: var(--accent-color);
            color: var(--text-color);
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }
        button:hover {
            background: var(--hover-color);
            transform: scale(1.05);
        }
        .attendance-card {
            background: var(--card-background);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 15px rgba(0,0,0,0.2);
            transition: all 0.3s;
        }
        .attendance-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.4);
        }
        .date-header {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--accent-color);
        }
        .hours-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            font-size: 0.9rem;
        }
        .overtime-input {
            width: 60px;
            padding: 0.5rem;
            background-color: var(--background-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            margin: 0 0.5rem;
            transition: all 0.2s;
        }
        .overtime-input:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        .action-buttons {
            margin-top: 1.5rem;
            text-align: right;
        }
        .save {
            background: var(--hover-color);
        }
        .save:hover {
            background: var(--accent-color);
        }
        i {
            transition: transform 0.3s;
            color: var(--accent-color);
        }
        button:hover i {
            transform: rotate(360deg);
        }
    </style>
</head>
<body>
    <div class="container">
        <h2><i class="fas fa-clock"></i> Attendance Records</h2>
        
        <form method="get" class="selection-form">
            <select name="employee_id" required>
                <option value="">Select Employee</option>
                <?php foreach ($employees as $employee): ?>
                    <option value="<?php echo $employee['employee_id']; ?>" 
                            <?php echo ($selected_employee == $employee['employee_id']) ? 'selected' : ''; ?>>
                        <?php echo $employee['full_name']; ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <i class="fas fa-calendar"></i>
            <input type="date" name="date_from" value="<?php echo $date_from; ?>" required>
            <i class="fas fa-arrow-right"></i>
            <input type="date" name="date_to" value="<?php echo $date_to; ?>" required>
            <button type="submit"><i class="fas fa-search"></i> View</button>
            <button type="button" onclick="window.location.href='../reports/payroll_section.php'"><i class="fas fa-home"></i> Payroll Report Home</button>
        </form>

        <form method="post">
            <?php foreach ($grouped_records as $date => $records): ?>
                <div class="attendance-card">
                    <div class="date-header">
                        <i class="far fa-calendar-alt"></i> <?php echo date('M d, Y (D)', strtotime($date)); ?>
                    </div>
                    <div class="hours-summary">
                        <div class="total-hours">
                            <i class="fas fa-business-time"></i> Regular: <span id="regular_<?php echo $date; ?>"><?php 
                                if(isset($daily_hours[$date])) {
                                    $hours = floor($daily_hours[$date]);
                                    $minutes = round(($daily_hours[$date] - $hours) * 60);
                                    echo $hours . 'h ' . $minutes . 'm';
                                } else {
                                    echo '0h 0m';
                                }
                            ?></span>
                            <br>
                            <i class="fas fa-calculator"></i> Total: <span id="final_<?php echo $date; ?>"><?php echo isset($daily_hours[$date]) ? ($daily_hours[$date] > 8 ? 8 : $daily_hours[$date]) : 0; ?></span>h
                        </div>
                        <div>
                            <i class="fas fa-plus-circle"></i> OT:
                            <input type="number" class="overtime-input" name="overtime_hours_<?php echo $date; ?>" 
                                   min="0" value="0" data-date="<?php echo $date; ?>"
                                   onchange="updateTotalHours('<?php echo $date; ?>')">h
                            <input type="number" class="overtime-input" name="overtime_minutes_<?php echo $date; ?>" 
                                   min="0" max="59" value="0" data-date="<?php echo $date; ?>"
                                   onchange="updateTotalHours('<?php echo $date; ?>')">m
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <?php if (!empty($grouped_records)): ?>
                <div class="action-buttons">
                    <input type="hidden" name="save_records" value="1">
                    <button type="submit" class="save"><i class="fas fa-save"></i> Save</button>
                </div>
            <?php endif; ?>
        </form>
    </div>

    <script>
        function updateTotalHours(date) {
            const hoursInput = document.querySelector(`input[name="overtime_hours_${date}"]`);
            const minutesInput = document.querySelector(`input[name="overtime_minutes_${date}"]`);
            const hours = parseInt(hoursInput.value) || 0;
            const minutes = parseInt(minutesInput.value) || 0;
            const overtimeTotal = hours + (minutes / 60);
            const finalTotal = 8 + overtimeTotal;
            document.getElementById('final_' + date).textContent = finalTotal.toFixed(2);
        }
    </script>
</body>
</html>
