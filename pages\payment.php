<?php
session_start();
include_once '../config/database.php';

// Get transaction and payment details
$transaction_id = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';
$customer_info = [];
$total = 0;

if ($transaction_id) {
    $sql = "SELECT t.*, cu.full_name 
    FROM transactions t
    INNER JOIN customers cu ON t.customer_id = cu.customer_id 
    WHERE t.transaction_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$transaction_id]);
    $customer_info = $stmt->fetch(PDO::FETCH_ASSOC);

    // Get total transaction amount
    $details_sql = "SELECT SUM(
        CASE 
            WHEN weight > 7 THEN base_price + ((weight - 7) * price_per_kg)
            ELSE base_price
        END
    ) as total_amount FROM transaction_details WHERE transaction_id = ?";
    $stmt = $conn->prepare($details_sql);
    $stmt->execute([$transaction_id]);
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total_amount'] ?? 0;
}

// Handle payment submission
// First, get the remaining balance from payment history
$balance_sql = "SELECT 
                CASE 
                    WHEN p.amount_paid IS NULL THEN p.transaction_amount 
                    ELSE (p.transaction_amount - p.amount_paid) 
                END as current_balance
                FROM payments p 
                WHERE p.transaction_id = ? 
                ORDER BY p.payment_id DESC LIMIT 1";
$stmt = $conn->prepare($balance_sql);
$stmt->execute([$transaction_id]);
$last_payment = $stmt->fetch(PDO::FETCH_ASSOC);
$current_balance = $last_payment ? $last_payment['current_balance'] : $total;

// Handle payment submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit_payment'])) {
    $method = $_POST['payment_method'];
    $transaction_amount = floatval($_POST['transaction_amount']);
    $amount_paid = floatval($_POST['amount_paid']);
    $points_used = max(0, intval($_POST['points_used'] ?? 0));
    $gcash_reference = $method === 'gcash' ? $_POST['gcash_reference'] : '';
    $balance = $current_balance - $amount_paid;

    try {
        $conn->beginTransaction();

        // If payment method is loyalty points, set amount_paid and balance to 0
        if ($method === 'loyalty') {
            $amount_paid = $transaction_amount;
            $balance = 0;
        } else {
            // For other payment methods, use the provided amount_paid and calculated balance
            $amount_paid = floatval($_POST['amount_paid']); 
            $balance = $current_balance - $amount_paid;
        }

        // Insert payment record
        $sql = "INSERT INTO payments (
            transaction_id, method, transaction_amount, 
            amount_paid, gcash_reference_number, points_used, balance
            ) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $transaction_id, $method, $transaction_amount,
            $amount_paid, $gcash_reference, $points_used, $balance
        ]);

        // Update transaction status only if balance is 0 or less
        if ($balance <= 0) {
            $update_transaction = "UPDATE transactions SET remarks = 'PAID' WHERE transaction_id = ?";
            $stmt = $conn->prepare($update_transaction);
            $stmt->execute([$transaction_id]);
        } else {
            $update_transaction = "UPDATE transactions SET remarks = 'NOT FULLY PAID' WHERE transaction_id = ?";
            $stmt = $conn->prepare($update_transaction);
            $stmt->execute([$transaction_id]);
        }

        // Update customer loyalty points if points were used
        if ($points_used > 0) {
            $update_points = "UPDATE loyalty_points 
                SET status = 'USED'
                WHERE customer_id = ? 
                AND status = 'AVAILABLE'
                AND points <= ?";
            $stmt = $conn->prepare($update_points);
            $stmt->execute([$customer_info['customer_id'], $points_used]);
        }

        $conn->commit();
        header("Location: payment.php?transaction_id=" . $transaction_id);
        exit();
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $error = "Payment processing failed: " . $e->getMessage();
    }
}



// Get existing payments
$payments_sql = "SELECT * FROM payments WHERE transaction_id = ? ORDER BY created_at DESC";
$stmt = $conn->prepare($payments_sql);
$stmt->execute([$transaction_id]);
$existing_payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>POS Payment</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .pos-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .pos-header {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .customer-info {
            background: white;
            border-left: 5px solid #3498db;
        }
        .amount-display {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .payment-form {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        .payment-history {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .action-buttons .btn {
            min-width: 150px;
        }
    </style>
</head>
<body>
    <div class="pos-container">
        <div class="pos-header">
            <h2><i class="fas fa-cash-register me-2"></i>POS Payment</h2>
        </div>

        <?php if ($customer_info): ?>
            <div class="row">
                <div class="col-md-4">
                    <div class="card customer-info shadow-sm mb-3">
                        <div class="card-body">
                            <h5 class="card-title text-primary mb-4">Customer Information</h5>
                            <div class="d-flex flex-column gap-3">
                                <div>
                                    <i class="fas fa-user-circle fa-2x text-primary mb-2"></i>
                                    <h4><?php echo htmlspecialchars($customer_info['full_name']); ?></h4>
                                </div>
                                <?php
                                $points_sql = "SELECT SUM(points) as total_points FROM loyalty_points WHERE customer_id = ? and status = 'AVAILABLE'";
                                $stmt = $conn->prepare($points_sql);
                                $stmt->execute([$customer_info['customer_id']]);
                                $loyalty_points = $stmt->fetch(PDO::FETCH_ASSOC)['total_points'] ?? 0;
                                ?>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-star text-warning"></i> Points:</span>
                                    <?php if($loyalty_points >= 10): ?>
                                        <span class="badge bg-warning text-dark" style="font-size: 1.5em"><?php echo number_format($loyalty_points, 0); ?></span>
                                    <?php else: ?>
                                        <span class="badge bg-warning text-dark"><?php echo number_format($loyalty_points, 0); ?></span>
                                    <?php endif; ?>
                                </div>
                                <?php if($loyalty_points >= 10): ?>
                                    <div class="alert alert-success mt-2">
                                        <i class="fas fa-gift"></i> Congratulations! You can claim your FREE laundry service!
                                    </div>
                                <?php endif; ?>
                                <div class="card shadow-sm p-3 mb-4 rounded border-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold text-secondary">
                                            <i class="fas fa-money-bill-wave text-success"></i> Total Due:
                                        </span>
                                        <span class="amount-display fs-4 fw-bold text-success">
                                            ₱<?php echo number_format($total, 2); ?>
                                        </span>
                                    </div>
                                    <p id="points_label" class="fw-bold text-start mt-3 text-danger"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <form method="POST" class="payment-form p-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label"><i class="fas fa-wallet me-2"></i>Payment Method</label>
                                <select name="payment_method" class="form-select form-select-lg" id="payment_method" required>
                                    <option value="cash">💵 Cash</option>
                                    <option value="gcash">📱 GCash</option>
                                    <option value="credit">💳 Credit</option>
                                    <option value="partial">⚡ Partial Payment</option>
                                    <option value="loyalty">⭐ Loyalty Points</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label"><i class="fas fa-money-bill-wave me-2"></i>Amount Paid</label>
                                <div class="input-group input-group-lg">
                                    <span class="input-group-text">₱</span>
                                    <input type="number" step="0.01" name="amount_paid" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="transaction_amount" value="<?php echo $total; ?>">

                        <div id="gcash_reference_div" style="display:none;" class="mb-3">
                            <label class="form-label"><i class="fas fa-hashtag me-2"></i>GCash Reference</label>
                            <input type="text" name="gcash_reference" class="form-control form-control-lg">
                        </div>

                        <div class="mb-4">
                            <label class="form-label"><i class="fas fa-star me-2"></i>Loyalty Points</label>
                            <div class="input-group input-group-lg">
                                <input type="number" name="points_used" id="points_used" class="form-control" 
                                       max="<?php echo $loyalty_points; ?>" value="0">
                                <button type="button" class="btn btn-warning" onclick="usePoints()">Use All Points</button>
                            </div>
                        </div>

                        <div class="action-buttons d-flex gap-2 justify-content-end">
                            <a href="transaction_details.php?transaction_id=<?php echo $transaction_id; ?>" 
                            class="btn btn-lg btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back
                            </a>
                            <button type="button" class="btn btn-lg btn-info" onclick="printReceipt()">
                                <i class="fas fa-print me-2"></i>Print Receipt
                            </button>
                            <button type="submit" name="submit_payment" class="btn btn-lg btn-primary">
                                <i class="fas fa-check-circle me-2"></i>Process Payment
                            </button>
                        </div>
                        <script>
                        function printReceipt() {
                            let amountPaid = document.querySelector('input[name="amount_paid"]').value;
                            let pointsUsed = document.querySelector('input[name="points_used"]').value;
                            let paymentMethod = document.querySelector('#payment_method').value;
                            let transactionId = new URLSearchParams(window.location.search).get('transaction_id');
                            
                            // Redirect to a PHP script that generates PDF
                            window.open(`../reports/generate_receipt.php?transaction_id=${transactionId}`, '_blank');
                        }
                        </script>
                    </form>

                    <?php if ($existing_payments): ?>
                    <div class="payment-history">
                        <h4 class="mb-3"><i class="fas fa-history me-2"></i>Payment History</h4>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Payment ID</th>
                                        <th>Date</th>
                                        <th>Method</th>
                                        <th>Amount Paid</th>
                                        <th>Points Used</th>
                                        <th>Balance</th>
                                        <th>Reference</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($existing_payments as $payment): ?>
                                    <tr>
                                        <td><?php echo $payment['payment_id']; ?></td>
                                        <td><?php echo date('M d, Y h:i A', strtotime($payment['created_at'])); ?></td>
                                        <td>
                                            <?php 
                                            $icon = match($payment['method']) {
                                                'cash' => '💵',
                                                'gcash' => '📱',
                                                'credit' => '💳',
                                                'partial' => '⚡',
                                                default => '💰'
                                            };
                                            echo $icon . ' ' . ucfirst($payment['method']); 
                                            ?>
                                        </td>
                                        <td>₱<?php echo number_format($payment['amount_paid'], 2); ?></td>
                                        <td><?php echo $payment['points_used'] ? number_format($payment['points_used']) : '-'; ?></td>
                                        <td>₱<?php echo number_format($payment['balance'], 2); ?></td>
                                        <td><?php echo $payment['gcash_reference_number'] ?: '-'; ?></td>
                                        <td>
                                            <form action="../includes/void_payment.php" method="POST" onsubmit="return confirm('Are you sure you want to void this payment?');">
                                                <input type="hidden" name="payment_id" value="<?php echo $payment['payment_id']; ?>">
                                                <input type="hidden" name="transaction_id" value="<?php echo $transaction_id; ?>">
                                                <button type="submit" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-trash"></i> Void
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>No transaction selected.
            </div>
        <?php endif; ?>
    </div>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        document.getElementById('payment_method').addEventListener('change', function() {
            const gcashDiv = document.getElementById('gcash_reference_div');
            gcashDiv.style.display = this.value === 'gcash' ? 'block' : 'none';
        });

        function usePoints() {
            const pointsInput = document.getElementById('points_used');
            const pointsUsed = parseInt(pointsInput.value) || 0;
            const maxPoints = <?php echo $loyalty_points; ?>;
            if (maxPoints < 10) {
            const pointsLabel = document.getElementById('points_label');
            pointsLabel.style.display = 'block';
            pointsLabel.textContent = "You're on your way to earning even more points! 🌟 Keep going, and you'll reach higher rewards in no time! 🚀";
                return;
            }
            const newPointsUsed = pointsUsed + maxPoints;
            pointsInput.value = newPointsUsed;
        }
    </script>
    <script>
        function calculateChange() {
            const amountDue = parseFloat(<?php echo $total; ?>);
            const amountTendered = parseFloat(document.getElementById('amount_tendered').value) || 0;
            const amountPaidInput = document.querySelector('input[name="amount_paid"]');
            const changeDisplay = document.getElementById('change_display');

            if (amountTendered >= amountDue) {
                amountPaidInput.value = amountDue;
                const change = amountTendered - amountDue;
                changeDisplay.textContent = '₱' + change.toFixed(2);
                changeDisplay.classList.remove('text-danger');
                changeDisplay.classList.add('text-success');
            } else {
                amountPaidInput.value = amountTendered;
                changeDisplay.textContent = 'Insufficient amount';
                changeDisplay.classList.remove('text-success');
                changeDisplay.classList.add('text-danger');
            }
        }

        // Add the existing event listeners here
        document.getElementById('payment_method').addEventListener('change', function() {
            const gcashDiv = document.getElementById('gcash_reference_div');
            gcashDiv.style.display = this.value === 'gcash' ? 'block' : 'none';
        });
    </script>
</body>
</html>
