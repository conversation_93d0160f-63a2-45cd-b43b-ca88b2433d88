<?php
/**
 * Loyalty Points Management System
 * Handles point earning, redemption, and validation
 */

class LoyaltyPointsManager {
    private $conn;
    
    // Point conversion rates
    const POINTS_PER_PESO_SPENT = 0.1; // 1 point per 10 pesos spent
    const PESO_VALUE_PER_POINT = 1.0;  // 1 point = 1 peso
    const MIN_POINTS_FOR_REDEMPTION = 10;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
    }
    
    /**
     * Award points for a transaction (backward compatible)
     */
    public function awardPoints($customer_id, $transaction_amount, $transaction_id = null) {
        try {
            $points_earned = floor($transaction_amount * self::POINTS_PER_PESO_SPENT);

            if ($points_earned > 0) {
                // Check if loyalty_points table has transaction_id column
                $column_check = $this->conn->query("SHOW COLUMNS FROM loyalty_points LIKE 'transaction_id'");
                $has_transaction_id = $column_check->rowCount() > 0;

                if ($has_transaction_id) {
                    $sql = "INSERT INTO loyalty_points (customer_id, points, status, transaction_id)
                            VALUES (?, ?, 'AVAILABLE', ?)";
                    $stmt = $this->conn->prepare($sql);
                    $stmt->execute([$customer_id, $points_earned, $transaction_id]);
                } else {
                    $sql = "INSERT INTO loyalty_points (customer_id, points, status)
                            VALUES (?, ?, 'AVAILABLE')";
                    $stmt = $this->conn->prepare($sql);
                    $stmt->execute([$customer_id, $points_earned]);
                }

                return $points_earned;
            }

            return 0;
        } catch (Exception $e) {
            error_log("Points award failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get available points for a customer
     */
    public function getAvailablePoints($customer_id) {
        $sql = "SELECT COALESCE(SUM(points), 0) as total_points 
                FROM loyalty_points 
                WHERE customer_id = ? AND status = 'AVAILABLE'";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$customer_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC)['total_points'];
    }
    
    /**
     * Redeem points for payment
     */
    public function redeemPoints($customer_id, $points_to_redeem) {
        try {
            $this->conn->beginTransaction();
            
            // Validate redemption
            $validation = $this->validateRedemption($customer_id, $points_to_redeem);
            if (!$validation['valid']) {
                $this->conn->rollBack();
                return $validation;
            }
            
            // Mark points as used (FIFO - First In, First Out)
            $sql = "UPDATE loyalty_points 
                    SET status = 'USED', updated_at = CURRENT_TIMESTAMP 
                    WHERE customer_id = ? AND status = 'AVAILABLE' 
                    ORDER BY loyalty_id ASC 
                    LIMIT ?";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$customer_id, $points_to_redeem]);
            
            $this->conn->commit();
            
            return [
                'valid' => true,
                'points_redeemed' => $points_to_redeem,
                'peso_value' => $points_to_redeem * self::PESO_VALUE_PER_POINT,
                'message' => 'Points redeemed successfully'
            ];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            error_log("Points redemption failed: " . $e->getMessage());
            return [
                'valid' => false,
                'message' => 'Points redemption failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Validate points redemption
     */
    public function validateRedemption($customer_id, $points_to_redeem) {
        if ($points_to_redeem < self::MIN_POINTS_FOR_REDEMPTION) {
            return [
                'valid' => false,
                'message' => 'Minimum ' . self::MIN_POINTS_FOR_REDEMPTION . ' points required for redemption'
            ];
        }
        
        $available_points = $this->getAvailablePoints($customer_id);
        
        if ($points_to_redeem > $available_points) {
            return [
                'valid' => false,
                'message' => 'Insufficient points. Available: ' . $available_points
            ];
        }
        
        return ['valid' => true, 'message' => 'Redemption valid'];
    }
    
    /**
     * Get points history for a customer
     */
    public function getPointsHistory($customer_id, $limit = 50) {
        $sql = "SELECT 
                    lp.*,
                    t.transaction_date,
                    CASE 
                        WHEN lp.status = 'AVAILABLE' THEN 'Earned'
                        WHEN lp.status = 'USED' THEN 'Redeemed'
                        ELSE lp.status
                    END as action_type
                FROM loyalty_points lp
                LEFT JOIN transactions t ON lp.transaction_id = t.transaction_id
                WHERE lp.customer_id = ?
                ORDER BY lp.updated_at DESC
                LIMIT ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$customer_id, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Calculate points value in pesos
     */
    public function calculatePointsValue($points) {
        return $points * self::PESO_VALUE_PER_POINT;
    }
    
    /**
     * Get customer points summary
     */
    public function getCustomerPointsSummary($customer_id) {
        $sql = "SELECT 
                    SUM(CASE WHEN status = 'AVAILABLE' THEN points ELSE 0 END) as available_points,
                    SUM(CASE WHEN status = 'USED' THEN points ELSE 0 END) as used_points,
                    SUM(points) as total_earned,
                    COUNT(DISTINCT CASE WHEN status = 'AVAILABLE' THEN loyalty_id END) as active_records
                FROM loyalty_points 
                WHERE customer_id = ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$customer_id]);
        $summary = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Add calculated values
        $summary['peso_value'] = $this->calculatePointsValue($summary['available_points']);
        $summary['can_redeem'] = $summary['available_points'] >= self::MIN_POINTS_FOR_REDEMPTION;
        
        return $summary;
    }
    
    /**
     * Restore points (for voided transactions)
     */
    public function restorePoints($customer_id, $points_to_restore) {
        try {
            $sql = "INSERT INTO loyalty_points (customer_id, points, status) 
                    VALUES (?, ?, 'AVAILABLE')";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$customer_id, $points_to_restore]);
            return true;
        } catch (Exception $e) {
            error_log("Points restoration failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clean up expired or invalid point records
     */
    public function cleanupPoints($customer_id = null) {
        try {
            // This could be extended to handle point expiration
            // For now, just remove any records with 0 points
            $sql = "DELETE FROM loyalty_points WHERE points <= 0";
            $params = [];
            
            if ($customer_id) {
                $sql .= " AND customer_id = ?";
                $params[] = $customer_id;
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->rowCount();
        } catch (Exception $e) {
            error_log("Points cleanup failed: " . $e->getMessage());
            return 0;
        }
    }
}
?>
