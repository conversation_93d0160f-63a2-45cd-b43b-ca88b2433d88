<?php
// Database connection parameters
include_once '../config/database.php'; 

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $name = $_POST['name'];
        $description = $_POST['description'];
        $price = $_POST['price'];
        $stock_quantity = $_POST['stock_quantity'];
        
        $sql = "INSERT INTO products (name, description, price, stock_quantity, created_at) 
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$name, $description, $price, $stock_quantity]);
    }
}

if (isset($_POST['update']) && isset($_POST['product_id'])) {
    $product_id = $_POST['product_id'];
    $name = $_POST['name'];
    $description = $_POST['description'];
    $price = $_POST['price'];
    $stock_quantity = $_POST['stock_quantity'];
    
    $sql = "UPDATE products SET name=?, description=?, price=?, stock_quantity=? WHERE product_id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$name, $description, $price, $stock_quantity, $product_id]);
}

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM products WHERE name LIKE ? OR description LIKE ?";
$search_term = "%$search%";
$stmt = $conn->prepare($sql);
$stmt->execute([$search_term, $search_term]);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Product Management</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; }
        .table thead th { background-color: #0d6efd; color: white; }
        .input-group { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Product Management</h2>
        
        <!-- Search Form -->
        <form method="GET" class="mb-4">
            <div class="input-group">
                <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                <input type="text" name="search" class="form-control" placeholder="Search products..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-primary"><i class="fa-solid fa-search"></i> Search</button>
            </div>
        </form>

        <!-- Modified Add/Edit Product Form -->
        <form method="POST" class="mb-4">
            <input type="hidden" name="product_id" id="product_id">
            <div class="row mb-3">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-box"></i></span>
                        <input type="text" name="name" class="form-control" placeholder="Product Name" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-file-lines"></i></span>
                        <input type="text" name="description" class="form-control" placeholder="Description">
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-dollar-sign"></i></span>
                        <input type="number" step="0.01" name="price" class="form-control" placeholder="Price" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-cubes"></i></span>
                        <input type="number" name="stock_quantity" class="form-control" placeholder="Stock Quantity" required>
                    </div>
                </div>
                <div class="col">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="submit" name="save" class="btn btn-success"><i class="fa-solid fa-plus"></i> Save New</button>
                        <button type="submit" name="update" class="btn btn-warning"><i class="fa-solid fa-pen"></i> Update</button>
                        <a href="../pages/pricing.php" class="btn btn-secondary"><i class="fa-solid fa-home"></i> Go Back to Pricing</a>
                    </div>
                </div>
            </div>
        </form>

        <!-- Modified Products Table -->
        <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-striped">
                <thead style="position: sticky; top: 0; background: white;">
                    <tr>
                        <th><i class="fa-solid fa-hashtag"></i> ID</th>
                        <th><i class="fa-solid fa-box"></i> Name</th>
                        <th><i class="fa-solid fa-file-lines"></i> Description</th>
                        <th><i class="fa-solid fa-dollar-sign"></i> Price</th>
                        <th><i class="fa-solid fa-cubes"></i> Stock</th>
                        <th><i class="fa-solid fa-calendar-days"></i> Created At</th>
                        <th><i class="fa-solid fa-gear"></i> Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($result as $row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['product_id']); ?></td>
                        <td><?php echo htmlspecialchars($row['name']); ?></td>
                        <td><?php echo htmlspecialchars($row['description']); ?></td>
                        <td><?php echo htmlspecialchars($row['price']); ?></td>
                        <td><?php echo htmlspecialchars($row['stock_quantity']); ?></td>
                        <td><?php echo htmlspecialchars($row['created_at']); ?></td>
                        <td>
                            <button onclick="editProduct(<?php echo htmlspecialchars(json_encode($row)); ?>)" class="btn btn-sm btn-primary">Edit</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function editProduct(product) {
        document.querySelector('[name="product_id"]').value = product.product_id;
        document.querySelector('[name="name"]').value = product.name;
        document.querySelector('[name="description"]').value = product.description;
        document.querySelector('[name="price"]').value = product.price;
        document.querySelector('[name="stock_quantity"]').value = product.stock_quantity;
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
