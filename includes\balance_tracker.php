<?php
/**
 * Balance Tracking System
 * Provides functions for accurate balance calculation and tracking
 */

class BalanceTracker {
    private $conn;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
    }
    
    /**
     * Calculate and update transaction totals (backward compatible)
     */
    public function updateTransactionTotals($transaction_id) {
        try {
            // Check if new schema exists
            $balance_check = $this->conn->query("SHOW COLUMNS FROM transactions LIKE 'total_amount'");
            $has_balance_tracking = $balance_check->rowCount() > 0;

            if (!$has_balance_tracking) {
                // Old schema - no balance tracking columns, just return true
                return true;
            }

            $this->conn->beginTransaction();

            // Calculate total from transaction details
            $details_sql = "SELECT COALESCE(SUM(subtotal), 0) as total_items
                           FROM transaction_details
                           WHERE transaction_id = ?";
            $stmt = $this->conn->prepare($details_sql);
            $stmt->execute([$transaction_id]);
            $total_items = $stmt->fetch(PDO::FETCH_ASSOC)['total_items'];

            // Calculate total from addons
            $addons_sql = "SELECT COALESCE(SUM(trans_subtotal), 0) as total_addons
                          FROM addons
                          WHERE transaction_id = ?";
            $stmt = $this->conn->prepare($addons_sql);
            $stmt->execute([$transaction_id]);
            $total_addons = $stmt->fetch(PDO::FETCH_ASSOC)['total_addons'];

            // Calculate total payments
            $payments_sql = "SELECT COALESCE(SUM(amount_paid), 0) as total_payments
                            FROM payments
                            WHERE transaction_id = ?";
            $stmt = $this->conn->prepare($payments_sql);
            $stmt->execute([$transaction_id]);
            $total_payments = $stmt->fetch(PDO::FETCH_ASSOC)['total_payments'];

            // Calculate totals and status
            $total_amount = $total_items + $total_addons;
            $balance = $total_amount - $total_payments;
            $status = $this->determineTransactionStatus($balance, $total_payments);

            // Update transaction
            $update_sql = "UPDATE transactions
                          SET total_amount = ?,
                              amount_paid = ?,
                              balance = ?,
                              status = ?
                          WHERE transaction_id = ?";
            $stmt = $this->conn->prepare($update_sql);
            $stmt->execute([$total_amount, $total_payments, $balance, $status, $transaction_id]);

            $this->conn->commit();
            return true;

        } catch (Exception $e) {
            $this->conn->rollBack();
            error_log("Balance update failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Determine transaction status based on balance
     */
    private function determineTransactionStatus($balance, $total_payments) {
        if ($balance <= 0) {
            return 'PAID';
        } elseif ($total_payments > 0) {
            return 'PARTIAL';
        } else {
            return 'PENDING';
        }
    }
    
    /**
     * Get transaction balance summary (backward compatible)
     */
    public function getTransactionSummary($transaction_id) {
        // Check if new schema exists
        $balance_check = $this->conn->query("SHOW COLUMNS FROM transactions LIKE 'total_amount'");
        $has_balance_tracking = $balance_check->rowCount() > 0;

        if ($has_balance_tracking) {
            // New schema with balance tracking
            $sql = "SELECT
                        t.*,
                        c.full_name,
                        c.phone_number,
                        c.address,
                        SUM(CASE WHEN lp.status = 'AVAILABLE' THEN lp.points ELSE 0 END) as available_points
                    FROM transactions t
                    JOIN customers c ON t.customer_id = c.customer_id
                    LEFT JOIN loyalty_points lp ON c.customer_id = lp.customer_id
                    WHERE t.transaction_id = ?
                    GROUP BY t.transaction_id";
        } else {
            // Old schema without balance tracking
            $sql = "SELECT
                        t.*,
                        c.full_name,
                        c.phone_number,
                        c.address,
                        SUM(CASE WHEN lp.status = 'AVAILABLE' THEN lp.points ELSE 0 END) as available_points,
                        0 as total_amount,
                        0 as amount_paid,
                        0 as balance,
                        'PENDING' as status
                    FROM transactions t
                    JOIN customers c ON t.customer_id = c.customer_id
                    LEFT JOIN loyalty_points lp ON c.customer_id = lp.customer_id
                    WHERE t.transaction_id = ?
                    GROUP BY t.transaction_id";
        }

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$transaction_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Process payment and update balances (backward compatible)
     */
    public function processPayment($transaction_id, $payment_data) {
        try {
            $this->conn->beginTransaction();

            // Insert payment record
            $payment_sql = "INSERT INTO payments (
                transaction_id, method, transaction_amount,
                amount_paid, gcash_reference_number, points_used
            ) VALUES (?, ?, ?, ?, ?, ?)";

            $stmt = $this->conn->prepare($payment_sql);
            $stmt->execute([
                $transaction_id,
                $payment_data['method'],
                $payment_data['transaction_amount'],
                $payment_data['amount_paid'],
                $payment_data['gcash_reference'] ?? null,
                $payment_data['points_used'] ?? 0
            ]);

            // Update transaction totals (only if new schema exists)
            $this->updateTransactionTotals($transaction_id);

            $this->conn->commit();
            return true;

        } catch (Exception $e) {
            $this->conn->rollBack();
            error_log("Payment processing failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get payment history for a transaction
     */
    public function getPaymentHistory($transaction_id) {
        $sql = "SELECT * FROM payments 
                WHERE transaction_id = ? 
                ORDER BY created_at DESC";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$transaction_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Validate payment amount against remaining balance (backward compatible)
     */
    public function validatePaymentAmount($transaction_id, $payment_amount) {
        $summary = $this->getTransactionSummary($transaction_id);
        if (!$summary) {
            return ['valid' => false, 'message' => 'Transaction not found'];
        }

        if ($payment_amount <= 0) {
            return ['valid' => false, 'message' => 'Payment amount must be greater than 0'];
        }

        // Calculate actual balance for old schema
        $balance = $summary['balance'];

        // If using old schema (balance is 0), calculate manually
        if ($balance == 0 && !isset($summary['total_amount'])) {
            // Calculate total from transaction details
            $details_sql = "SELECT COALESCE(SUM(subtotal), 0) as total_items FROM transaction_details WHERE transaction_id = ?";
            $stmt = $this->conn->prepare($details_sql);
            $stmt->execute([$transaction_id]);
            $total_items = $stmt->fetch(PDO::FETCH_ASSOC)['total_items'];

            // Calculate total from addons
            $addons_sql = "SELECT COALESCE(SUM(trans_subtotal), 0) as total_addons FROM addons WHERE transaction_id = ?";
            $stmt = $this->conn->prepare($addons_sql);
            $stmt->execute([$transaction_id]);
            $total_addons = $stmt->fetch(PDO::FETCH_ASSOC)['total_addons'];

            // Calculate total payments
            $payments_sql = "SELECT COALESCE(SUM(amount_paid), 0) as total_payments FROM payments WHERE transaction_id = ?";
            $stmt = $this->conn->prepare($payments_sql);
            $stmt->execute([$transaction_id]);
            $total_payments = $stmt->fetch(PDO::FETCH_ASSOC)['total_payments'];

            $balance = ($total_items + $total_addons) - $total_payments;
        }

        if ($payment_amount > $balance) {
            return [
                'valid' => false,
                'message' => 'Payment amount exceeds remaining balance of ₱' . number_format($balance, 2)
            ];
        }

        return ['valid' => true, 'message' => 'Payment amount is valid'];
    }
}
?>
