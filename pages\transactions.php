<?php
session_start();

include_once '../config/database.php'; 

// Handle form submissions for new transactions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save_transaction'])) {
        $customer_id = $_POST['customer_id'];
        $remarks = $_POST['remarks'];
        
        // Validate customer_id
        if (empty($customer_id)) {
            $_SESSION['error'] = "Please select a customer first.";
            header("Location: transactions.php");
            exit();
        }
        
        try {
            $conn->beginTransaction();
            
            // Debug output
            error_log("Attempting to create transaction for customer_id: " . $customer_id);
            
            // Insert transaction
            $sql = "INSERT INTO transactions (customer_id, transaction_date, remarks) 
                    VALUES (:customer_id, NOW(), :remarks)";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':customer_id', $customer_id, PDO::PARAM_INT);
            $stmt->bindParam(':remarks', $remarks, PDO::PARAM_STR);
            $stmt->execute();
            
            // Get the last inserted transaction ID
            $transaction_id = $conn->lastInsertId();
            
            error_log("Transaction created with ID: " . $transaction_id);
            
            // Insert loyalty points for the transaction
            $points = 1; // Default points per transaction
            $sql = "INSERT INTO loyalty_points (customer_id, points, status) 
                    VALUES (:customer_id, :points, 'AVAILABLE')";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':customer_id', $customer_id, PDO::PARAM_INT);
            $stmt->bindParam(':points', $points, PDO::PARAM_INT);
            $stmt->execute();
            
            $conn->commit();
            
            $_SESSION['success'] = "Transaction created successfully!";
            
            // Redirect to prevent form resubmission
            header("Location: transactions.php?customer_id=" . $customer_id);
            exit();
            
        } catch (Exception $e) {
            $conn->rollBack();
            error_log("Error creating transaction: " . $e->getMessage());
            $_SESSION['error'] = "Error creating transaction: " . $e->getMessage();
            header("Location: transactions.php");
            exit();
        }
    }
}

// Get all customers for the dropdown
$sql = "SELECT customer_id, full_name FROM customers ORDER BY full_name";
$stmt = $conn->prepare($sql);
$stmt->execute();
$customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get transactions for selected customer
$selected_customer = isset($_GET['customer_id']) ? $_GET['customer_id'] : '';
$transactions = [];

// Get transactions within date range for selected customer
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

if ($selected_customer) {
    $sql = "SELECT DISTINCT DATE(transaction_date) AS date, transactions.*, customers.full_name 
            FROM transactions 
            INNER JOIN customers ON transactions.customer_id = customers.customer_id 
            WHERE transactions.customer_id = ? 
            AND DATE(transaction_date) BETWEEN ? AND ? 
            ORDER BY transaction_date DESC";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$selected_customer, $start_date, $end_date]);
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

$_SESSION['customer_id'] = $selected_customer; // Store customer_id in session

// Display any error or success messages
if (isset($_SESSION['error'])) {
    echo '<div class="alert alert-danger">' . $_SESSION['error'] . '</div>';
    unset($_SESSION['error']);
}
if (isset($_SESSION['success'])) {
    echo '<div class="alert alert-success">' . $_SESSION['success'] . '</div>';
    unset($_SESSION['success']);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>POS System - Transactions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-light">
        <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #3498DB;
            --accent-color: #ECF0F1;
            --success-color: #27AE60;
            --danger-color: #E74C3C;
        }
        
        body { 
            font-family: 'Nunito', sans-serif;
            background: var(--accent-color);
            min-height: 100vh;
        }
        
        .container { 
            max-width: 1400px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--accent-color);
        }
        
        .page-title {
            color: var(--primary-color);
            font-weight: 700;
            margin: 0;
        }
        
        .btn {
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--secondary-color);
            border: none;
        }
        
        .btn-primary:hover {
            background: darken(var(--secondary-color), 10%);
            transform: translateY(-2px);
        }
        
        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-top: 2rem;
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background: var(--accent-color);
            color: var(--primary-color);
            font-weight: 700;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
            padding: 1rem;
        }
        
        .table td {
            padding: 1rem;
            vertical-align: middle;
        }
        
        .badge {
            padding: 0.5rem 1rem;
            font-weight: 600;
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-header {
            background: var(--accent-color);
            border-radius: 15px 15px 0 0;
        }
        
        .form-control {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            border: 2px solid var(--accent-color);
        }
        
        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: none;
        }
    </style>
    <div class="container">
        <div class="page-header">
            <h2 class="page-title">Point of Sale - Transactions</h2>
            <a href="../index.php" class="btn btn-outline-secondary">
                <i class="fa-solid fa-home"></i> Dashboard
            </a>
        </div>

        <!-- Customer Selection Form -->
        <div class="row g-4">
            <div class="col-md-6">
                <form method="GET" class="d-flex">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fa-solid fa-user text-primary"></i>
                        </span>
                        <select name="customer_id" class="form-select" onchange="this.form.submit()">
                            <option value="">Select Customer...</option>
                            <?php foreach ($customers as $customer): ?>
                            <option value="<?php echo $customer['customer_id']; ?>" 
                                <?php echo ($selected_customer == $customer['customer_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($customer['full_name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </form>
            </div>
            <div class="col-md-6 text-end">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newTransactionModal">
                    <i class="fa-solid fa-plus"></i> New Transaction
                </button>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="table-container">
            <div style="max-height: 600px; overflow-y: auto;">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>#ID</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Transaction Total</th>
                            <th>Outstanding Balance</th>
                            <th>Remarks</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($transactions)): ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fa-solid fa-info-circle text-info me-2"></i>
                                No transactions found for the selected date range
                            </td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($transactions as $transaction): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($transaction['transaction_id']); ?></td>
                            <td><?php echo htmlspecialchars($transaction['full_name']); ?></td>
                            <td><?php echo date('M d, Y', strtotime($transaction['transaction_date'])); ?></td>
                            <td>
                                <?php
                                    $sql_total = "SELECT COALESCE(SUM(subtotal), 0) as total 
                                                  FROM transaction_details 
                                                  WHERE transaction_id = ?";
                                    $stmt_total = $conn->prepare($sql_total);
                                    $stmt_total->execute([$transaction['transaction_id']]);
                                    $total = $stmt_total->fetch(PDO::FETCH_ASSOC)['total'];
                                    echo $total > 0 ? '₱' . number_format($total, 2) : '<span class="badge bg-success">NEW</span>';
                                ?>
                            </td>
                            <td>
                                <?php
                                    $sql_total = "SELECT transaction_amount, amount_paid FROM payments WHERE transaction_id = ? ORDER BY payment_id DESC LIMIT 1";
                                    $stmt_total = $conn->prepare($sql_total);
                                    $stmt_total->execute([$transaction['transaction_id']]);
                                    $payment = $stmt_total->fetch(PDO::FETCH_ASSOC);
                                    
                                    if ($payment) {
                                        $balance = $payment['transaction_amount'] - $payment['amount_paid'];
                                        echo '<div class="badge bg-danger rounded-pill px-3 py-2">
                                                <i class="fa-solid fa-peso-sign me-1"></i>' . 
                                                number_format($balance, 2) . 
                                             '</div>';
                                    } else {
                                        // If no payment record, show transaction amount as balance
                                        $sql_trans = "SELECT COALESCE(SUM(subtotal), 0) as transaction_amount 
                                                     FROM transaction_details 
                                                     WHERE transaction_id = ?";
                                        $stmt_trans = $conn->prepare($sql_trans);
                                        $stmt_trans->execute([$transaction['transaction_id']]);
                                        $trans_amount = $stmt_trans->fetch(PDO::FETCH_ASSOC)['transaction_amount'];
                                        
                                        if ($trans_amount > 0) {
                                            echo '<div class="badge bg-danger rounded-pill px-3 py-2">
                                                    <i class="fa-solid fa-peso-sign me-1"></i>' . 
                                                    number_format($trans_amount, 2) . 
                                                 '</div>';
                                        } else {
                                            echo '<span class="badge bg-success rounded-pill px-3">NEW</span>';
                                        }
                                    }
                                ?>
                            </td>
                            <td><?php echo htmlspecialchars($transaction['remarks']); ?></td>
                            <td class="text-end">
                                <a href="transaction_details.php?transaction_id=<?php echo $transaction['transaction_id']; ?>" 
                                   class="btn btn-info btn-sm text-white">
                                    <i class="fa-solid fa-shirt"></i> Details
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- New Transaction Modal -->
    <div class="modal fade" id="newTransactionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title">New Transaction</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="customer_id" value="<?php echo $selected_customer; ?>">
                        <div class="mb-3">
                            <label class="form-label">Remarks</label>
                            <textarea name="remarks" class="form-control" rows="3" required>In Process</textarea>
                        </div>
                    </div>
                    <div class="modal-footer border-0">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="save_transaction" class="btn btn-primary">
                            <i class="fa-solid fa-save"></i> Save Transaction
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
