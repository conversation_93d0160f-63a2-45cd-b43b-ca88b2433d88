<?php
/**
 * Test script to verify balance validation is working correctly
 */

include_once 'config/database.php';
include_once 'includes/balance_tracker.php';

echo "<!DOCTYPE html><html><head><title>Balance Validation Test</title></head><body>";
echo "<h1>Balance Validation Test</h1>";

try {
    $balanceTracker = new BalanceTracker($conn);
    
    // Get a sample transaction
    $sql = "SELECT transaction_id FROM transactions LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $sample_transaction = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sample_transaction) {
        $transaction_id = $sample_transaction['transaction_id'];
        echo "<h2>Testing Transaction ID: $transaction_id</h2>";
        
        // Get transaction summary
        $summary = $balanceTracker->getTransactionSummary($transaction_id);
        echo "<h3>Transaction Summary:</h3>";
        echo "<ul>";
        echo "<li>Customer: " . htmlspecialchars($summary['full_name']) . "</li>";
        echo "<li>Summary Balance: ₱" . number_format($summary['balance'] ?? 0, 2) . "</li>";
        echo "</ul>";
        
        // Calculate manual balance
        echo "<h3>Manual Balance Calculation:</h3>";
        
        // Calculate total from transaction details
        $details_sql = "SELECT COALESCE(SUM(subtotal), 0) as total_items FROM transaction_details WHERE transaction_id = ?";
        $stmt = $conn->prepare($details_sql);
        $stmt->execute([$transaction_id]);
        $total_items = $stmt->fetch(PDO::FETCH_ASSOC)['total_items'];
        
        // Calculate total from addons
        $addons_sql = "SELECT COALESCE(SUM(trans_subtotal), 0) as total_addons FROM addons WHERE transaction_id = ?";
        $stmt = $conn->prepare($addons_sql);
        $stmt->execute([$transaction_id]);
        $total_addons = $stmt->fetch(PDO::FETCH_ASSOC)['total_addons'];
        
        // Calculate total payments
        $payments_sql = "SELECT COALESCE(SUM(amount_paid), 0) as total_payments FROM payments WHERE transaction_id = ?";
        $stmt = $conn->prepare($payments_sql);
        $stmt->execute([$transaction_id]);
        $total_payments = $stmt->fetch(PDO::FETCH_ASSOC)['total_payments'];
        
        $grand_total = $total_items + $total_addons;
        $calculated_balance = $grand_total - $total_payments;
        
        echo "<ul>";
        echo "<li>Items Total: ₱" . number_format($total_items, 2) . "</li>";
        echo "<li>Addons Total: ₱" . number_format($total_addons, 2) . "</li>";
        echo "<li>Grand Total: ₱" . number_format($grand_total, 2) . "</li>";
        echo "<li>Total Payments: ₱" . number_format($total_payments, 2) . "</li>";
        echo "<li><strong>Calculated Balance: ₱" . number_format($calculated_balance, 2) . "</strong></li>";
        echo "</ul>";
        
        // Test payment validation with different amounts
        echo "<h3>Payment Validation Tests:</h3>";
        
        $test_amounts = [
            $calculated_balance * 0.5, // Half balance
            $calculated_balance,        // Full balance
            $calculated_balance * 1.5   // Over balance
        ];
        
        foreach ($test_amounts as $i => $test_amount) {
            if ($test_amount <= 0) continue;
            
            echo "<h4>Test " . ($i + 1) . ": Payment of ₱" . number_format($test_amount, 2) . "</h4>";
            
            $validation = $balanceTracker->validatePaymentAmount($transaction_id, $test_amount);
            
            if ($validation['valid']) {
                echo "<p style='color: green;'>✅ " . $validation['message'] . "</p>";
            } else {
                echo "<p style='color: red;'>❌ " . $validation['message'] . "</p>";
            }
        }
        
        // Test with small amount that should always work
        if ($calculated_balance > 0) {
            echo "<h4>Test: Small Payment of ₱1.00</h4>";
            $validation = $balanceTracker->validatePaymentAmount($transaction_id, 1.00);
            
            if ($validation['valid']) {
                echo "<p style='color: green;'>✅ " . $validation['message'] . "</p>";
            } else {
                echo "<p style='color: red;'>❌ " . $validation['message'] . "</p>";
            }
        }
        
    } else {
        echo "<p>❌ No transactions found in database.</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='pages/transaction_details.php'>← Back to Transaction Details</a></p>";
echo "</body></html>";
?>
