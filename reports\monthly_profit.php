<?php
include_once '../config/database.php';

$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

$profit_records = [];
$total_sales = 0;
$total_expenses = 0;
$total_profit = 0;

if ($start_date && $end_date) {
    // Get daily sales and expenses
    $sql = "SELECT 
                dates.profit_date,
                COALESCE(sales.daily_sales, 0) as daily_sales,
                COALESCE(expenses.daily_expenses, 0) as daily_expenses
    FROM (
        SELECT DATE(transaction_date) as profit_date
        FROM transactions
        WHERE DATE(transaction_date) BETWEEN ? AND ?
        UNION
        SELECT DATE(expense_date)
        FROM expenses
        WHERE DATE(expense_date) BETWEEN ? AND ?
    ) dates
    LEFT JOIN (
        SELECT DATE(t.transaction_date) as sale_date, SUM(td.subtotal) as daily_sales
        FROM transactions t
        INNER JOIN transaction_details td ON t.transaction_id = td.transaction_id
        WHERE DATE(t.transaction_date) BETWEEN ? AND ?
        GROUP BY DATE(t.transaction_date)
            ) sales ON dates.profit_date = sales.sale_date
            LEFT JOIN (
                SELECT DATE(expense_date) as expense_date, SUM(amount) as daily_expenses
                FROM expenses
                WHERE DATE(expense_date) BETWEEN ? AND ?
                GROUP BY DATE(expense_date)
            ) expenses ON dates.profit_date = expenses.expense_date
            ORDER BY dates.profit_date";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$start_date, $end_date, $start_date, $end_date, $start_date, $end_date, $start_date, $end_date]);
    $profit_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate totals
    $sql = "SELECT 
            (SELECT SUM(td.subtotal) 
             FROM transaction_details td 
             INNER JOIN transactions t ON t.transaction_id = td.transaction_id
             WHERE DATE(t.transaction_date) BETWEEN ? AND ?) as total_sales,
            (SELECT SUM(amount) 
             FROM expenses 
             WHERE DATE(expense_date) BETWEEN ? AND ?) as total_expenses";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$start_date, $end_date, $start_date, $end_date]);
    $totals = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $total_sales = $totals['total_sales'] ?? 0;
    $total_expenses = $totals['total_expenses'] ?? 0;
    $total_profit = $total_sales - $total_expenses;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Monthly Profit Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background-color: #f8f9fa; }
        .card { border: none; border-radius: 15px; }
        .form-control { border-radius: 10px; }
        .btn { border-radius: 10px; }
        .table { background-color: white; border-radius: 15px; overflow: hidden; }
        .profit-positive { color: #198754; }
        .profit-negative { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="card shadow-lg mb-4">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="card-title h3 mb-0">
                        <i class="fa-solid fa-chart-line me-2 text-primary"></i>Monthly Profit Report
                    </h2>
                    <a href="../pages/reports.php" class="btn btn-outline-secondary">
                        <i class="fa-solid fa-arrow-left me-2"></i>Back
                    </a>
                </div>
                
                <form method="GET" class="needs-validation" novalidate>
                    <div class="row g-3">
                        <div class="col-md-5">
                            <label class="form-label">Start Date</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                <input type="date" name="start_date" class="form-control" 
                                       value="<?php echo $start_date; ?>" required>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">End Date</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                <input type="date" name="end_date" class="form-control" 
                                       value="<?php echo $end_date; ?>" required>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fa-solid fa-search me-2"></i>Generate
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <?php if (!empty($profit_records)): ?>
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white shadow-lg">
                    <div class="card-body p-4">
                        <h6 class="text-white-50 mb-2">Total Sales</h6>
                        <h3 class="mb-0">₱<?php echo number_format($total_sales, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-danger text-white shadow-lg">
                    <div class="card-body p-4">
                        <h6 class="text-white-50 mb-2">Total Expenses</h6>
                        <h3 class="mb-0">₱<?php echo number_format($total_expenses, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white shadow-lg">
                    <div class="card-body p-4">
                        <h6 class="text-white-50 mb-2">Net Profit</h6>
                        <h3 class="mb-0">₱<?php echo number_format($total_profit, 2); ?></h3>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="card shadow-lg">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th class="px-4">Date</th>
                                <th class="text-end px-4">Daily Sales</th>
                                <th class="text-end px-4">Daily Expenses</th>
                                <th class="text-end px-4">Daily Profit</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($profit_records as $record): 
                                $daily_profit = $record['daily_sales'] - $record['daily_expenses'];
                                $profit_class = $daily_profit >= 0 ? 'profit-positive' : 'profit-negative';
                            ?>
                                <tr>
                                    <td class="px-4"><?php echo date('M d, Y', strtotime($record['profit_date'])); ?></td>
                                    <td class="text-end px-4">₱<?php echo number_format($record['daily_sales'], 2); ?></td>
                                    <td class="text-end px-4">₱<?php echo number_format($record['daily_expenses'], 2); ?></td>
                                    <td class="text-end px-4 <?php echo $profit_class; ?>">
                                        ₱<?php echo number_format($daily_profit, 2); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
