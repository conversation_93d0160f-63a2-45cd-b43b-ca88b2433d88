<?php
// Include database connection
include_once '../config/database.php'; 

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Initialize response array
$response = [
    'totalSales' => 0,
    'totalExpenses' => 0,
    'error' => null
];

try {
    // Get first and last day of current month
    $firstDayOfMonth = date('Y-m-01');
    $lastDayOfMonth = date('Y-m-t');

    // Get total sales
    $salesQuery = "SELECT COALESCE(SUM(td.subtotal), 0) as total_sales 
                  FROM transaction_details td 
                  INNER JOIN transactions t ON td.transaction_id = t.transaction_id 
                  WHERE DATE(t.transaction_date) BETWEEN ? AND ?";
    
    $stmt = $conn->prepare($salesQuery);
    $stmt->execute([$firstDayOfMonth, $lastDayOfMonth]);
    $salesRow = $stmt->fetch(PDO::FETCH_ASSOC);
    $response['totalSales'] = floatval($salesRow['total_sales']);

    // Get total expenses
    $expensesQuery = "SELECT COALESCE(SUM(amount), 0) as total_expenses 
                     FROM expenses 
                     WHERE DATE(expense_date) BETWEEN ? AND ?";
    
    $stmt = $conn->prepare($expensesQuery);
    $stmt->execute([$firstDayOfMonth, $lastDayOfMonth]);
    $expensesRow = $stmt->fetch(PDO::FETCH_ASSOC);
    $response['totalExpenses'] = floatval($expensesRow['total_expenses']);

    // Calculate monthly profit
    $response['monthlyProfit'] = $response['totalSales'] - $response['totalExpenses'];

} catch (Exception $e) {
    $response['error'] = $e->getMessage();
} finally {
    // Close database connection
    if (isset($conn)) {
        $conn = null;
    }
}

// Output JSON response
header('Content-Type: application/json');
echo json_encode($response);
?>
