<?php
require('../fpdf/fpdf.php');
require('../config/database.php');


// Fetch all employees for the dropdown
$sql = "SELECT * FROM profile";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single row

// Ensure business_name is retrieved correctly
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad"; 
$business_owner = $profile['business_owner'] ??""; 
$business_address = $profile['business_address'] ??""; 
$business_cell = $profile['business_cell'] ??""; 
$business_land = $profile['business_land'] ??""; 

$transaction_id = $_GET['transaction_id'] ?? '';
class ModernPDF extends FPDF {
    function Header() {
        // Logo (replace path with your logo)
        // $this->Image('logo.png', 10, 10, 30);
        
        // Company name
        $this->SetFont('Arial', 'B', 18);
        $this->Cell(0, 10, $GLOBALS['business_name'], 0, 1, 'C');
        
        // Address
        $this->SetFont('Arial', '', 8);
        $this->Cell(0, 5, $GLOBALS['business_address'], 0, 1, 'C');
        $this->Cell(0, 5, 'Phone: ' . $GLOBALS['business_cell'] . ' | Landline: ' . $GLOBALS['business_land'] . ' ', 0, 1, 'C');
        $this->Cell(0, 5, 'Owed and Managed by: ' . $GLOBALS['business_owner'] .  ' ', 0, 1, 'C');
        
        // Line break
        $this->Ln(10);
    }
    
    function Footer() {
        $this->SetY(-15);
        $this->SetFont('Arial', 'I', 8);
        $this->Cell(0, 10, 'Page '.$this->PageNo().'/{nb}', 0, 0, 'C');
    }
}
$pdf = new ModernPDF('P', 'mm', 'A4');
$pdf->AddPage();

// Get transaction details (your existing SQL query remains the same)
$sql = "SELECT t.*, td.category, td.weight, td.subtotal, td.base_price, td.price_per_kg,
    c.customer_id, c.full_name as customer_name, t.transaction_date, 
    (SELECT SUM(amount_paid) FROM payments WHERE transaction_id = t.transaction_id) as payment_amount 
    FROM transactions t 
    JOIN customers c ON t.customer_id = c.customer_id 
    JOIN transaction_details td ON t.transaction_id = td.transaction_id
    WHERE t.transaction_id = ?";

    // Calculate total, amount paid and balance
    $sql = "SELECT 
        t.*, 
        td.category, 
        td.weight, 
        td.subtotal, 
        td.base_price, 
        td.price_per_kg,
        c.customer_id, 
        c.full_name as customer_name, 
        t.transaction_date,
        (SELECT SUM(subtotal) FROM transaction_details WHERE transaction_id = t.transaction_id) as total_amount,
        (SELECT SUM(amount_paid) FROM payments WHERE transaction_id = t.transaction_id) as payment_amount,
        (SELECT SUM(subtotal) FROM transaction_details WHERE transaction_id = t.transaction_id) - 
        (SELECT COALESCE(SUM(amount_paid), 0) FROM payments WHERE transaction_id = t.transaction_id) as balance
        FROM transactions t 
        JOIN customers c ON t.customer_id = c.customer_id 
        JOIN transaction_details td ON t.transaction_id = td.transaction_id
        WHERE t.transaction_id = ?";

$stmt = $conn->prepare($sql);
$stmt->execute([$transaction_id]);
$transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
$balance = $transactions[0]['balance'];

$pdf = new ModernPDF('P', 'mm', 'A4');
$pdf->AddPage();

// Your existing SQL queries remain the same
// [SQL code remains unchanged...]

// Reset text color and set smaller margins
$pdf->SetTextColor(0, 0, 0);
$pdf->SetMargins(10, 10, 10);

// Customer details in a compact format
$pdf->SetY(30);
$pdf->SetFont('Arial', 'B', 9);
$pdf->Cell(95, 5, 'Customer Details', 0, 0);
$pdf->Cell(95, 5, 'Transaction ID: ' . $transaction_id, 0, 1, 'R');

$pdf->SetFont('Arial', '', 8);
$pdf->Cell(95, 4, 'ID: ' . $transactions[0]['customer_id'] . ' | Name: ' . $transactions[0]['customer_name'], 0, 0);
$pdf->Cell(95, 4, 'Date: ' . date('F d, Y', strtotime($transactions[0]['transaction_date'])), 0, 1, 'R');

// Table Header with compact styling
$pdf->SetY(42);
$pdf->SetFillColor(51, 51, 51);
$pdf->SetTextColor(255, 255, 255);
$pdf->SetFont('Arial', 'B', 8);
$pdf->Cell(50, 6, 'Category', 1, 0, 'C', true);
$pdf->Cell(25, 6, 'Weight (kg)', 1, 0, 'C', true);
$pdf->Cell(25, 6, 'Price/kg', 1, 0, 'C', true);
$pdf->Cell(25, 6, 'Base Price', 1, 0, 'C', true);
$pdf->Cell(35, 6, 'Subtotal', 1, 1, 'C', true);

// Table Content
$pdf->SetTextColor(0, 0, 0);
$pdf->SetFont('Arial', '', 8);
$total = 0;
$alternate = false;

foreach ($transactions as $item) {
    $pdf->SetFillColor($alternate ? 245 : 255, $alternate ? 245 : 255, $alternate ? 245 : 255);
    $pdf->Cell(50, 5, $item['category'], 1, 0, 'L', true);
    $pdf->Cell(25, 5, number_format($item['weight'], 2), 1, 0, 'R', true);
    $pdf->Cell(25, 5, number_format($item['price_per_kg'], 2), 1, 0, 'R', true);
    $pdf->Cell(25, 5, number_format($item['base_price'], 2), 1, 0, 'R', true);
    $pdf->Cell(35, 5, number_format($item['subtotal'], 2), 1, 1, 'R', true);
    $total += $item['subtotal'];
    $alternate = !$alternate;
}

// Total section
$pdf->SetFillColor(51, 51, 51);
$pdf->SetTextColor(255, 255, 255);
$pdf->SetFont('Arial', 'B', 8);
$pdf->Cell(125, 6, 'Total:', 1, 0, 'R', true);
$pdf->Cell(35, 6, number_format($total, 2), 1, 1, 'R', true);

// Payment details in compact format
$pdf->SetTextColor(0, 0, 0);
$pdf->SetFont('Arial', '', 8);
$pdf->Cell(125, 5, 'Amount Paid:', 0, 0, 'R');
$pdf->Cell(35, 5, 'PHP ' . number_format($transactions[0]['payment_amount'], 2), 0, 1, 'R');
$pdf->Cell(125, 5, 'Balance:', 0, 0, 'R');
$pdf->Cell(35, 5, 'PHP ' . number_format($balance, 2), 0, 1, 'R');

$pdf->Output();
?>
