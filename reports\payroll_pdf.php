<?php
require('../fpdf/fpdf.php');
include '../config/database.php';

// Fetch business profile
$sql = "SELECT * FROM profile";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single row

// Ensure business_name is retrieved correctly
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad"; 
$business_owner = $profile['business_owner'] ??""; 
$business_address = $profile['business_address'] ??""; 
$business_cell = $profile['business_cell'] ??""; 
$business_land = $profile['business_land'] ??""; 

if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    $employee_id = $_GET['employee_id'] ?? '';
    $year = $_GET['year'] ?? date('Y');
    $month = $_GET['month'] ?? date('n');

    // Create new PDF instance
    $pdf = new FPDF('P', 'mm', 'A4');
    $pdf->AddPage();

    // Set font
    $pdf->SetFont('Arial', 'B', 16);

    // Header
    // Company name
    $pdf->SetFont('Arial', 'B', 18);
    $pdf->Cell(0, 10, $business_name, 0, 1, 'C');
    
    // Address
    $pdf->SetFont('Arial', '', 8);
    $pdf->Cell(0, 5, $business_address, 0, 1, 'C');
    $pdf->Cell(0, 5, 'Phone: ' . $business_cell . ' | Landline: ' . $business_land, 0, 1, 'C');
    $pdf->Cell(0, 5, 'Owned and Managed by: ' . $business_owner, 0, 1, 'C');
    
    // Line break
    $pdf->Ln(10);

    // Get employee payroll data
    $query = "SELECT 
        monthly_data.employee_id,
        monthly_data.monthly_total_hours,
        monthly_data.total_overtime,
        FORMAT((monthly_data.monthly_total_hours * (monthly_data.daily_rate / 8)), 2) AS monthly_total_pay,
        FORMAT(COALESCE(deductions_data.total_deductions, 0), 2) AS total_deductions,
        FORMAT(((monthly_data.monthly_total_hours * (monthly_data.daily_rate / 8)) - COALESCE(deductions_data.total_deductions, 0)), 2) AS monthly_final_pay,
        e.full_name
    FROM (
        SELECT 
            das.employee_id,
            SUM(das.total_hours) AS monthly_total_hours,
            SUM(das.overtime_hours) AS total_overtime,
            MAX(p.daily_rate) AS daily_rate
        FROM daily_attendance_summary das
        JOIN pay p ON p.employee_id = das.employee_id
        WHERE das.employee_id = :employee_id
        AND YEAR(das.date) = :year
        AND MONTH(das.date) = :month
        GROUP BY das.employee_id
    ) monthly_data
    LEFT JOIN (
        SELECT employee_id, COALESCE(SUM(deduct_fees), 0) AS total_deductions
        FROM deductions
        WHERE deduct_month = :month
        AND deduct_year = :year
        GROUP BY employee_id
    ) deductions_data ON monthly_data.employee_id = deductions_data.employee_id
    JOIN employees e ON e.employee_id = monthly_data.employee_id";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(':employee_id', $employee_id, PDO::PARAM_INT);
    $stmt->bindParam(':year', $year, PDO::PARAM_INT);
    $stmt->bindParam(':month', $month, PDO::PARAM_INT);
    $stmt->execute();
    $payroll = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($payroll) {
        // Define modern color scheme
        $primaryColor = [46, 204, 113]; // Green
        $secondaryColor = [52, 73, 94]; // Dark Blue-Gray
        $accentColor = [241, 196, 15]; // Yellow
        $textDark = [44, 62, 80]; // Dark Gray
        $textLight = [236, 240, 241]; // Light Gray

        // Document Title
        // Get month name and format period
        $monthName = date('F', mktime(0, 0, 0, $month, 1));
        $pdf->SetFont('Arial', '', 12);
        $pdf->Cell(0, 8, 'PAYROLL PERIOD: ' . $monthName . ' ' . $year, 0, 1, 'C');
        $pdf->SetFillColor($primaryColor[0], $primaryColor[1], $primaryColor[2]);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFont('Arial', 'B', 16);
        $pdf->Cell(0, 15, ' PAYROLL STATEMENT', 1, 1, 'C', true);
        
        $pdf->SetFont('Arial', '', 12);
        $pdf->Cell(0, 8, 'PAYROLL PERIOD: ' . $monthName . ' ' . $year, 0, 1, 'C');
        // Employee Details Box
        $pdf->Ln(5);
        $pdf->SetFillColor($secondaryColor[0], $secondaryColor[1], $secondaryColor[2]);
        $pdf->Cell(0, 12, ' Employee Details', 1, 1, 'L', true);
        
        $pdf->SetTextColor($textDark[0], $textDark[1], $textDark[2]);
        $pdf->SetFont('Arial', '', 11);
        $pdf->Cell(60, 8, ' Employee Name:', 'L', 0);
        $pdf->SetFont('Arial', 'B', 11);
        $pdf->Cell(0, 8, $payroll['full_name'], 'R', 1);

        // Work Hours Summary
        $pdf->Ln(5);
        $pdf->SetFillColor($accentColor[0], $accentColor[1], $accentColor[2]);
        $pdf->SetTextColor($textDark[0], $textDark[1], $textDark[2]);
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->Cell(0, 12, ' WORK HOURS BREAKDOWN', 1, 1, 'C', true);

        $pdf->SetFont('Arial', '', 11);
        $pdf->Cell(120, 10, ' Standard Hours:', 1, 0);
        $pdf->Cell(0, 10, ($payroll['monthly_total_hours'] - $payroll['total_overtime']) . ' hrs', 1, 1, 'R');
        
        $pdf->Cell(120, 10, ' Overtime Hours:', 1, 0);
        $pdf->Cell(0, 10, $payroll['total_overtime'] . ' hrs', 1, 1, 'R');
        
        $pdf->SetFont('Arial', 'B', 11);
        $pdf->Cell(120, 10, ' TOTAL HOURS:', 1, 0);
        $pdf->Cell(0, 10, $payroll['monthly_total_hours'] . ' hrs', 1, 1, 'R');

        // Compensation Details
        $pdf->Ln(5);
        $pdf->SetFillColor($primaryColor[0], $primaryColor[1], $primaryColor[2]);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->Cell(0, 12, ' COMPENSATION', 1, 1, 'C', true);

        $pdf->SetTextColor($textDark[0], $textDark[1], $textDark[2]);
        $pdf->SetFont('Arial', '', 11);
        $pdf->Cell(120, 10, ' Gross Earnings:', 1, 0);
        $pdf->Cell(0, 10, 'P ' . $payroll['monthly_total_pay'], 1, 1, 'R');

        // Deductions
        $deduction_query = "SELECT deduct_des, deduct_fees 
                          FROM deductions 
                          WHERE employee_id = :employee_id 
                          AND deduct_year = :year 
                          AND deduct_month = :month";
        $deduction_stmt = $conn->prepare($deduction_query);
        $deduction_stmt->bindParam(':employee_id', $employee_id, PDO::PARAM_INT);
        $deduction_stmt->bindParam(':year', $year, PDO::PARAM_INT);
        $deduction_stmt->bindParam(':month', $month, PDO::PARAM_INT);
        $deduction_stmt->execute();
        $deductions = $deduction_stmt->fetchAll(PDO::FETCH_ASSOC);

        $pdf->SetFillColor($secondaryColor[0], $secondaryColor[1], $secondaryColor[2]);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFont('Arial', 'B', 12);
        $pdf->Cell(0, 10, ' DEDUCTIONS', 1, 1, 'L', true);

        $pdf->SetTextColor($textDark[0], $textDark[1], $textDark[2]);
        $pdf->SetFont('Arial', '', 11);
        foreach ($deductions as $deduction) {
            $pdf->Cell(120, 8, ' ' . $deduction['deduct_des'], 1, 0);
            $pdf->Cell(0, 8, 'P ' . number_format($deduction['deduct_fees'], 2), 1, 1, 'R');
        }

        $pdf->SetFont('Arial', 'B', 11);
        $pdf->Cell(120, 10, ' Total Deductions:', 1, 0);
        $pdf->Cell(0, 10, 'P ' . $payroll['total_deductions'], 1, 1, 'R');

        // Final Pay
        $pdf->Ln(5);
        $pdf->SetFillColor($primaryColor[0], $primaryColor[1], $primaryColor[2]);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->Cell(120, 15, ' NET PAY:', 1, 0, 'L', true);
        $pdf->Cell(0, 15, 'P ' . $payroll['monthly_final_pay'], 1, 1, 'R', true);

        // Signature Section
        $pdf->Ln(20);
        $pdf->SetTextColor($textDark[0], $textDark[1], $textDark[2]);
        $pdf->Line(20, $pdf->GetY(), 90, $pdf->GetY());
        $pdf->Line(120, $pdf->GetY(), 190, $pdf->GetY());
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(90, 5, 'Employee Signature', 0, 0, 'C');
        $pdf->Cell(90, 5, 'Authorized Signature', 0, 1, 'C');

        $pdf->Output();
    } else {
        echo "No payroll data found for the selected period.";
    }
}
