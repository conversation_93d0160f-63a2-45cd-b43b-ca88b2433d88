<?php
/**
 * Database Schema Check and Migration Script
 * This script checks the current database schema and applies necessary migrations
 */

include_once '../config/database.php';

function checkAndMigrate($conn) {
    $migrations_applied = [];
    $errors = [];
    
    try {
        echo "<h2>Database Schema Check and Migration</h2>\n";
        
        // Check if princing_detail table exists (typo version)
        $check_princing = $conn->query("SHOW TABLES LIKE 'princing_detail'");
        $has_princing_typo = $check_princing->rowCount() > 0;
        
        // Check if pricing_detail table exists (correct version)
        $check_pricing = $conn->query("SHOW TABLES LIKE 'pricing_detail'");
        $has_pricing_correct = $check_pricing->rowCount() > 0;
        
        // Migration 1: Fix table name typo
        if ($has_princing_typo && !$has_pricing_correct) {
            echo "🔧 Fixing table name: princing_detail → pricing_detail<br>\n";
            $conn->exec("RENAME TABLE `princing_detail` TO `pricing_detail`");
            $migrations_applied[] = "Fixed table name typo";
        }
        
        // Check if addons table has the typo
        $check_addons = $conn->query("SHOW COLUMNS FROM addons LIKE 'adons_id'");
        $has_addons_typo = $check_addons->rowCount() > 0;
        
        // Migration 2: Fix addons column name
        if ($has_addons_typo) {
            echo "🔧 Fixing column name: adons_id → addons_id<br>\n";
            $conn->exec("ALTER TABLE `addons` CHANGE COLUMN `adons_id` `addons_id` INT(10) NOT NULL AUTO_INCREMENT");
            $migrations_applied[] = "Fixed addons column name typo";
        }
        
        // Check if transactions table has balance tracking columns
        $check_balance = $conn->query("SHOW COLUMNS FROM transactions LIKE 'total_amount'");
        $has_balance_tracking = $check_balance->rowCount() > 0;
        
        // Migration 3: Add balance tracking columns
        if (!$has_balance_tracking) {
            echo "🔧 Adding balance tracking columns to transactions table<br>\n";
            $conn->exec("ALTER TABLE `transactions` 
                        ADD COLUMN `total_amount` DECIMAL(10,2) DEFAULT 0.00 AFTER `transaction_date`,
                        ADD COLUMN `amount_paid` DECIMAL(10,2) DEFAULT 0.00 AFTER `total_amount`,
                        ADD COLUMN `balance` DECIMAL(10,2) DEFAULT 0.00 AFTER `amount_paid`,
                        ADD COLUMN `status` ENUM('PENDING', 'PARTIAL', 'PAID', 'CANCELLED') DEFAULT 'PENDING' AFTER `balance`");
            $migrations_applied[] = "Added balance tracking columns";
        }
        
        // Check if transaction_details has category_id column
        $check_category_id = $conn->query("SHOW COLUMNS FROM transaction_details LIKE 'category_id'");
        $has_category_id = $check_category_id->rowCount() > 0;
        
        // Migration 4: Add category_id column and migrate data
        if (!$has_category_id) {
            echo "🔧 Adding category_id column and migrating category data<br>\n";
            
            // Add the new column
            $conn->exec("ALTER TABLE `transaction_details` ADD COLUMN `category_id` INT(10) AFTER `transaction_id`");
            
            // Migrate existing data
            $migrate_sql = "UPDATE transaction_details td 
                           JOIN pricing p ON p.category = td.category 
                           SET td.category_id = p.id";
            $conn->exec($migrate_sql);
            
            // Handle any unmapped categories
            $unmapped_sql = "SELECT DISTINCT category FROM transaction_details 
                            WHERE category_id IS NULL OR category_id = 0";
            $stmt = $conn->prepare($unmapped_sql);
            $stmt->execute();
            $unmapped = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($unmapped as $row) {
                if (!empty($row['category'])) {
                    // Create missing category
                    $insert_cat = "INSERT IGNORE INTO pricing (category) VALUES (?)";
                    $stmt = $conn->prepare($insert_cat);
                    $stmt->execute([$row['category']]);
                    
                    // Update transaction_details with new category_id
                    $update_td = "UPDATE transaction_details td 
                                 JOIN pricing p ON p.category = td.category 
                                 SET td.category_id = p.id 
                                 WHERE td.category = ?";
                    $stmt = $conn->prepare($update_td);
                    $stmt->execute([$row['category']]);
                }
            }
            
            $migrations_applied[] = "Added category_id column and migrated data";
        }
        
        // Migration 5: Add foreign key constraints (if they don't exist)
        try {
            echo "🔧 Adding foreign key constraints<br>\n";
            
            // Check if foreign keys exist
            $fk_check = $conn->query("SELECT CONSTRAINT_NAME FROM information_schema.KEY_COLUMN_USAGE 
                                     WHERE TABLE_SCHEMA = DATABASE() 
                                     AND TABLE_NAME = 'transaction_details' 
                                     AND CONSTRAINT_NAME = 'fk_transaction_details_category'");
            
            if ($fk_check->rowCount() == 0 && $has_category_id) {
                $conn->exec("ALTER TABLE `transaction_details` 
                            ADD CONSTRAINT `fk_transaction_details_category` 
                            FOREIGN KEY (`category_id`) REFERENCES `pricing` (`id`) ON UPDATE CASCADE ON DELETE RESTRICT");
                $migrations_applied[] = "Added foreign key constraint for transaction_details";
            }
        } catch (Exception $e) {
            $errors[] = "Foreign key constraint creation failed: " . $e->getMessage();
        }
        
        // Migration 6: Add indexes for performance
        try {
            echo "🔧 Adding performance indexes<br>\n";
            
            $indexes = [
                "CREATE INDEX IF NOT EXISTS `idx_transactions_customer_date` ON `transactions` (`customer_id`, `transaction_date`)",
                "CREATE INDEX IF NOT EXISTS `idx_payments_transaction_date` ON `payments` (`transaction_id`, `created_at`)",
                "CREATE INDEX IF NOT EXISTS `idx_loyalty_points_customer_status` ON `loyalty_points` (`customer_id`, `status`)",
                "CREATE INDEX IF NOT EXISTS `idx_transaction_details_transaction` ON `transaction_details` (`transaction_id`)",
                "CREATE INDEX IF NOT EXISTS `idx_addons_transaction` ON `addons` (`transaction_id`)"
            ];
            
            foreach ($indexes as $index_sql) {
                $conn->exec($index_sql);
            }
            
            $migrations_applied[] = "Added performance indexes";
        } catch (Exception $e) {
            $errors[] = "Index creation failed: " . $e->getMessage();
        }
        
        // Migration 7: Update existing transaction totals
        if ($has_balance_tracking) {
            echo "🔧 Updating existing transaction totals<br>\n";
            
            $update_totals_sql = "
                UPDATE transactions t
                SET 
                    total_amount = (
                        SELECT COALESCE(SUM(td.subtotal), 0) + COALESCE(SUM(a.trans_subtotal), 0)
                        FROM transaction_details td
                        LEFT JOIN addons a ON td.transaction_id = a.transaction_id
                        WHERE td.transaction_id = t.transaction_id
                    ),
                    amount_paid = (
                        SELECT COALESCE(SUM(p.amount_paid), 0)
                        FROM payments p
                        WHERE p.transaction_id = t.transaction_id
                    )
            ";
            
            $conn->exec($update_totals_sql);
            
            // Update balance and status
            $conn->exec("UPDATE transactions SET balance = total_amount - amount_paid");
            $conn->exec("UPDATE transactions SET status = CASE 
                            WHEN balance <= 0 THEN 'PAID'
                            WHEN amount_paid > 0 THEN 'PARTIAL'
                            ELSE 'PENDING'
                        END");
            
            $migrations_applied[] = "Updated existing transaction totals";
        }
        
        echo "<h3>✅ Migration Summary</h3>\n";
        if (!empty($migrations_applied)) {
            echo "<ul>\n";
            foreach ($migrations_applied as $migration) {
                echo "<li>✅ $migration</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p>✅ Database schema is already up to date!</p>\n";
        }
        
        if (!empty($errors)) {
            echo "<h3>⚠️ Warnings/Errors</h3>\n";
            echo "<ul>\n";
            foreach ($errors as $error) {
                echo "<li>⚠️ $error</li>\n";
            }
            echo "</ul>\n";
        }
        
        echo "<p><strong>Migration completed successfully!</strong></p>\n";
        echo "<p><a href='../pages/transaction_details.php'>← Back to Transaction Details</a></p>\n";
        
    } catch (Exception $e) {
        echo "<h3>❌ Migration Failed</h3>\n";
        echo "<p>Error: " . $e->getMessage() . "</p>\n";
        echo "<p>Please check your database connection and permissions.</p>\n";
    }
}

// Run the migration if accessed directly
if (basename($_SERVER['PHP_SELF']) == 'check_and_migrate.php') {
    echo "<!DOCTYPE html><html><head><title>Database Migration</title></head><body>";
    checkAndMigrate($conn);
    echo "</body></html>";
}
?>
