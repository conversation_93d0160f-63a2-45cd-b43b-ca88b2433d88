<?php
session_start();
include_once '../config/database.php';

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $category = $_POST['category'];
        
        $sql = "INSERT INTO pricing (category) VALUES (?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$category]);
    }
}

if (isset($_POST['update']) && isset($_POST['id'])) {
    $id = $_POST['id'];
    $category = $_POST['category'];
    
    $sql = "UPDATE pricing SET category=? WHERE id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$category, $id]);
}

// Fetch pricing data
$sql = "SELECT id, category FROM pricing";
$stmt = $conn->prepare($sql);
$stmt->execute();
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Pricing Management</title>
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; }
        .table thead th { background-color: #0d6efd; color: white; }
        .input-group { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Category Management</h2>

        <!-- Add/Edit Pricing Form -->
        <form method="POST" class="mb-4">
            <input type="hidden" name="id" id="id">
            <div class="row mb-3">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-tag"></i></span>
                        <input type="text" name="category" class="form-control" required>
                    </div>
                </div>
                <div class="col">
                    <div class="btn-group" role="group">
                        <button type="submit" name="save" class="btn btn-success"><i class="fas fa-save"></i> Save New</button>
                        <button type="submit" name="update" class="btn btn-warning"><i class="fas fa-edit"></i> Update</button>
                        <a href="../index.php" class="btn btn-secondary"><i class="fas fa-home"></i> Home</a>
                        <a href="pricing_details.php" class="btn btn-info"><i class="fas fa-cogs"></i> Services Management</a>
                        <a href="product_management.php" class="btn btn-primary"><i class="fas fa-box"></i> Products</a>
                    </div>
                </div>
            </div>
        </form>

        <!-- Pricing Table -->
        <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-striped">
                <thead style="position: sticky; top: 0; background: white;">
                    <tr>
                        <th>ID</th>
                        <th>Category</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($result as $row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['id']); ?></td>
                        <td><?php echo htmlspecialchars($row['category']); ?></td>
                        <td>
                            <button onclick="editPricing(<?php echo htmlspecialchars(json_encode($row)); ?>)" 
                                    class="btn btn-sm btn-primary">Edit</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function editPricing(pricing) {
        document.querySelector('[name="id"]').value = pricing.id;
        document.querySelector('[name="category"]').value = pricing.category;
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
