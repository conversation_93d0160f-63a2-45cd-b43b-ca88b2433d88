<?php
session_start();
require('../fpdf/fpdf.php');
include('../config/database.php');

// Fetch all employees for the dropdown
$sql = "SELECT * FROM profile";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single row

// Ensure business_name is retrieved correctly
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad"; 
$business_owner = $profile['business_owner'] ??""; 
$business_address = $profile['business_address'] ??""; 
$business_cell = $profile['business_cell'] ??""; 
$business_land = $profile['business_land'] ??""; 

function get_employee_name($employee_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT full_name as fullname FROM employees WHERE employee_id = ?");
    $stmt->execute([$employee_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['fullname'] : 'Unknown';
}

if(!isset($_SESSION['employee_id'])) {
    header('Location: ../index.php');
    exit();
}

// Custom PDF class with header and footer
class PDF extends FPDF {
    function Header() {
        // Logo (replace path with your logo)
        // $this->Image('logo.png', 10, 10, 30);
        
        // Company name
        $this->SetFont('Arial', 'B', 18);
        $this->Cell(0, 10, $GLOBALS['business_name'], 0, 1, 'C');
        
        // Address
        $this->SetFont('Arial', '', 8);
        $this->Cell(0, 5, $GLOBALS['business_address'], 0, 1, 'C');
        $this->Cell(0, 5, 'Phone: ' . $GLOBALS['business_cell'] . ' | Landline: ' . $GLOBALS['business_land'] . ' ', 0, 1, 'C');
        $this->Cell(0, 5, 'Owed and Managed by: ' . $GLOBALS['business_owner'] .  ' ', 0, 1, 'C');
        
        // Line break
        $this->Ln(10);
    }
    
    function Footer() {
        $this->SetY(-15);
        $this->SetFont('Arial', 'I', 8);
        $this->Cell(0, 10, 'Page '.$this->PageNo().'/{nb}', 0, 0, 'C');
    }
}

if(isset($_GET['employee_id']) && isset($_GET['start_date']) && isset($_GET['end_date'])) {
    $employee_id = $_GET['employee_id'];
    $start_date = $_GET['start_date'];
    $end_date = $_GET['end_date'];
    
    // Create PDF object
    $pdf = new PDF('P', 'mm', 'Letter');
    $pdf->AliasNbPages();
    $pdf->AddPage();
    
    // Title
    $pdf->SetFont('Arial', 'B', 16);
    $pdf->Cell(0, 10, 'DAILY TIME RECORD', 0, 1, 'C');
    
    // Gray background for employee details
    $pdf->SetFillColor(240, 240, 240);
    $pdf->Rect(10, $pdf->GetY(), 195, 30, 'F');
    
    // Employee details
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(30, 10, 'Name:', 0, 0);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(80, 10, get_employee_name($employee_id), 0, 0);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(30, 10, 'Period:', 0, 0);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(0, 10, date('F d', strtotime($start_date)).' - '.date('F d, Y', strtotime($end_date)), 0, 1);
    
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(30, 10, 'ID No:', 0, 0);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(0, 10, $employee_id, 0, 1);
    
    // Table header with colored background
    $pdf->Ln(10);
    $pdf->SetFillColor(50, 50, 50);
    $pdf->SetTextColor(255, 255, 255);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(40, 8, 'Date', 1, 0, 'C', true);
    $pdf->Cell(40, 8, 'Time In', 1, 0, 'C', true);
    $pdf->Cell(40, 8, 'Time Out', 1, 0, 'C', true);
    $pdf->Cell(35, 8, 'Hours Worked', 1, 0, 'C', true);
    $pdf->Cell(40, 8, 'Remarks', 1, 1, 'C', true);

    // Reset text color for data
    $pdf->SetTextColor(0, 0, 0);
    $pdf->SetFont('Arial', '', 9);

    // Fetch and display attendance data
    $sql = "SELECT DATE(date) as attendance_date,
            MAX(CASE WHEN status = 'IN' THEN time END) as time_in,
            MAX(CASE WHEN status = 'OUT' THEN time END) as time_out
            FROM attendance 
            WHERE employee_id = ? 
            AND DATE(date) BETWEEN ? AND ?
            GROUP BY DATE(date)
            ORDER BY DATE(date)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$employee_id, $start_date, $end_date]);
    $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $total_hours = 0;
    foreach($attendance_records as $row) {
        $hours = '';
        if ($row['time_in'] && $row['time_out']) {
            $time1 = strtotime($row['time_in']);
            $time2 = strtotime($row['time_out']);
            $hours = round(($time2 - $time1) / 3600, 2);
            $total_hours += $hours;
        }
        
        $pdf->Cell(40, 7, date('M d, Y', strtotime($row['attendance_date'])), 1, 0, 'C');
        $pdf->Cell(40, 7, $row['time_in'] ? date('h:i A', strtotime($row['time_in'])) : '-', 1, 0, 'C');
        $pdf->Cell(40, 7, $row['time_out'] ? date('h:i A', strtotime($row['time_out'])) : '-', 1, 0, 'C');
        $pdf->Cell(35, 7, $hours ? number_format($hours, 2) : '-', 1, 0, 'C');
        $pdf->Cell(40, 7, '', 1, 1, 'C');
    }

    // Total hours
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(120, 7, 'Total Hours:', 1, 0, 'R');
    $pdf->Cell(35, 7, number_format($total_hours, 2), 1, 0, 'C');
    $pdf->Cell(40, 7, '', 1, 1, 'C');

    // Signature section
    $pdf->Ln(20);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(95, 5, 'Certified Correct:', 0, 0);
    $pdf->Cell(95, 5, 'Approved by:', 0, 1);

    $pdf->Ln(15);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(95, 5, '_____________________', 0, 0, 'C');
    $pdf->Cell(95, 5, '_____________________', 0, 1, 'C');
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(95, 5, 'Employee Signature', 0, 0, 'C');
    $pdf->Cell(95, 5, 'KENNETH LEONIDA', 0, 1, 'C');
    $pdf->Cell(95, 5, '', 0, 0, 'C');
    $pdf->Cell(95, 5, 'Owner', 0, 1, 'C');

    // Output PDF
    $pdf->Output('attendance_record.pdf', 'I');
    exit();
}
?>