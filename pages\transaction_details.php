<?php
include_once '../config/database.php';
include_once '../includes/balance_tracker.php';
include_once '../includes/loyalty_points.php';
include_once '../includes/validation.php';
session_start();

// Initialize helper classes
$balanceTracker = new BalanceTracker($conn);
$loyaltyManager = new LoyaltyPointsManager($conn);
$validator = new TransactionValidator($conn);

// Initialize variables
$transaction_id = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';
$customer_info = [];
$total_amount = 0;
$addons_total = 0;
$balance = 0;

// Redirect if no transaction ID provided
if (empty($transaction_id)) {
    $_SESSION['error_message'] = "Transaction ID is required.";
    header("Location: transactions.php");
    exit();
}

// Get transaction and customer details (backward compatible)
if ($transaction_id) {
    // Check if new schema exists
    $balance_check = $conn->query("SHOW COLUMNS FROM transactions LIKE 'total_amount'");
    $has_balance_tracking = $balance_check->rowCount() > 0;

    if ($has_balance_tracking) {
        // Use new balance tracking system
        $customer_info = $balanceTracker->getTransactionSummary($transaction_id);

        if (!$customer_info) {
            $_SESSION['error_message'] = "Transaction not found.";
            header("Location: transactions.php");
            exit();
        }

        // Ensure transaction totals are up to date
        $balanceTracker->updateTransactionTotals($transaction_id);

        // Refresh data after update
        $customer_info = $balanceTracker->getTransactionSummary($transaction_id);

        $TotalPayment = $customer_info['amount_paid'] ?? 0;
    } else {
        // Use old system for backward compatibility
        $sql = "SELECT t.*, c.full_name, c.phone_number, c.address,
                       SUM(CASE WHEN lp.status = 'AVAILABLE' THEN lp.points ELSE 0 END) as available_points
                FROM transactions t
                INNER JOIN customers c ON t.customer_id = c.customer_id
                LEFT JOIN loyalty_points lp ON c.customer_id = lp.customer_id
                WHERE t.transaction_id = ?
                GROUP BY t.transaction_id";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$transaction_id]);
        $customer_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$customer_info) {
            $_SESSION['error_message'] = "Transaction not found.";
            header("Location: transactions.php");
            exit();
        }

        // Get total amount paid for this transaction (old method)
        $payment_sql = "SELECT SUM(amount_paid) as TotalPayment FROM payments WHERE transaction_id = ?";
        $stmt = $conn->prepare($payment_sql);
        $stmt->execute([$transaction_id]);
        $total_payment = $stmt->fetch(PDO::FETCH_ASSOC);
        $TotalPayment = $total_payment['TotalPayment'] ?? 0;

        // Add calculated fields for compatibility
        $customer_info['amount_paid'] = $TotalPayment;
    }
}

// Get existing transaction details (backward compatible)
// Check if new schema exists
$column_check = $conn->query("SHOW COLUMNS FROM transaction_details LIKE 'category_id'");
$has_category_id = $column_check->rowCount() > 0;

if ($has_category_id) {
    // New schema with category_id
    $details_sql = "SELECT
                        td.*,
                        p.category as category_name
                    FROM transaction_details td
                    LEFT JOIN pricing p ON td.category_id = p.id
                    WHERE td.transaction_id = ?
                    ORDER BY td.detail_id";
} else {
    // Old schema with category as TEXT
    $details_sql = "SELECT
                        td.*,
                        td.category as category_name
                    FROM transaction_details td
                    WHERE td.transaction_id = ?
                    ORDER BY td.detail_id";
}

$stmt = $conn->prepare($details_sql);
$stmt->execute([$transaction_id]);
$transaction_details = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get addon products
$addons_sql = "SELECT a.*, p.name, p.price, p.stock_quantity
               FROM addons a
               JOIN products p ON a.product_id = p.product_id
               WHERE a.transaction_id = ?";
$stmt = $conn->prepare($addons_sql);
$stmt->execute([$transaction_id]);
$addon_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate total amounts (for display purposes - actual totals come from database)
foreach ($transaction_details as $detail) {
    $total_amount += $detail['subtotal'];
}
foreach ($addon_products as $addon) {
    $addons_total += $addon['trans_subtotal'];
}

// Calculate totals (backward compatible)
if (isset($has_balance_tracking) && $has_balance_tracking && isset($customer_info['total_amount'])) {
    // Use database values from new schema
    $grand_total = $customer_info['total_amount'];
    $amount_paid = $customer_info['amount_paid'];
    $balance = $customer_info['balance'];
} else {
    // Calculate manually for old schema
    $grand_total = $total_amount + $addons_total;
    $amount_paid = $TotalPayment;
    $balance = $grand_total - $amount_paid;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // 🔧 Ensure transaction_id is initialized
    $transaction_id = $_GET['transaction_id'] ?? null;
    $customer_id    = $customer_info['customer_id'] ?? null;

    if (isset($_POST['save_details'])) {
        // Sanitize input data
        $input_data = $validator->sanitizeInput($_POST);
        $category = $input_data['category'];
        $weight = floatval($input_data['weight']);
        $item_remarks = $input_data['item_remarks'] ?? '';

        // Validate business rules
        $business_validation = $validator->validateBusinessRules($transaction_id, 'add_item');
        if (!$business_validation['valid']) {
            $_SESSION['error_message'] = implode(', ', $business_validation['errors']);
            header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }

        // Validate transaction item data
        $item_validation = $validator->validateTransactionItem([
            'category' => $category,
            'weight' => $weight,
            'item_remarks' => $item_remarks
        ]);

        if (!$item_validation['valid']) {
            $_SESSION['error_message'] = implode(', ', $item_validation['errors']);
            header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }

        // Validate pricing configuration
        $pricing_validation = $validator->validatePricingExists($category, $weight);
        if (!$pricing_validation['valid']) {
            $_SESSION['error_message'] = $pricing_validation['error'];
            header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }

        $pricing = $pricing_validation['pricing'];

        $base_price = $pricing['price'];
        $price_per_kg = $pricing['price'];

        // Calculate subtotal based on pricing structure
        if ($pricing['num_load'] > 0) {
            // Load-based pricing
            $loads = ceil($weight / $pricing['kilogram']);
            $subtotal = $base_price * $loads;
        } else {
            // Weight-based pricing
            $subtotal = $price_per_kg * $weight;
        }

        // Insert transaction detail (backward compatible)
        if ($has_category_id) {
            // New schema with category_id
            $sql = "INSERT INTO transaction_details
                    (transaction_id, category_id, weight, price_per_kg, subtotal, item_remarks, base_price)
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$transaction_id, $category, $weight, $price_per_kg, $subtotal, $item_remarks, $base_price]);
        } else {
            // Old schema with category as TEXT - get category name
            $cat_sql = "SELECT category FROM pricing WHERE id = ?";
            $cat_stmt = $conn->prepare($cat_sql);
            $cat_stmt->execute([$category]);
            $category_name = $cat_stmt->fetch(PDO::FETCH_ASSOC)['category'] ?? 'Unknown';

            $sql = "INSERT INTO transaction_details
                    (transaction_id, category, weight, price_per_kg, subtotal, item_remarks, base_price)
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$transaction_id, $category_name, $weight, $price_per_kg, $subtotal, $item_remarks, $base_price]);
        }

        // Update transaction totals (if new schema exists)
        if (isset($has_balance_tracking) && $has_balance_tracking) {
            $balanceTracker->updateTransactionTotals($transaction_id);
        }

        $_SESSION['success_message'] = "Item added successfully!";
        header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }

    if (isset($_POST['add_product'])) {
        // Sanitize input data
        $input_data = $validator->sanitizeInput($_POST);
        $product_id = $input_data['product_id'];
        $quantity = floatval($input_data['quantity']);

        // Validate business rules
        $business_validation = $validator->validateBusinessRules($transaction_id, 'add_product');
        if (!$business_validation['valid']) {
            $_SESSION['error_message'] = implode(', ', $business_validation['errors']);
            header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }

        // Validate addon product data
        $product_validation = $validator->validateAddonProduct([
            'product_id' => $product_id,
            'quantity' => $quantity
        ]);

        if (!$product_validation['valid']) {
            $_SESSION['error_message'] = implode(', ', $product_validation['errors']);
            header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }

        $product = $product_validation['product'];

        if ($product['stock_quantity'] >= $quantity) {
            $subtotal = $quantity * $product['price'];
            $sql = "INSERT INTO addons (transaction_id, product_id, trans_qty, trans_subtotal)
                    VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$transaction_id, $product_id, $quantity, $subtotal]);

            $update_stock = "UPDATE products SET stock_quantity = stock_quantity - ? WHERE product_id = ?";
            $stmt = $conn->prepare($update_stock);
            $stmt->execute([$quantity, $product_id]);

            // Update transaction totals (if new schema exists)
            if (isset($has_balance_tracking) && $has_balance_tracking) {
                $balanceTracker->updateTransactionTotals($transaction_id);
            }

            $_SESSION['success_message'] = "Add-on product added successfully!";
        } else {
            $_SESSION['error_message'] = "Insufficient stock quantity!";
        }

        header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }   
}

//Process payments

if (isset($_POST['process_payment'])) {
    // Sanitize input data
    $input_data = $validator->sanitizeInput($_POST);
    $payment_method = $input_data['payment_method'];
    $amount_paid = floatval($input_data['amount_paid'] ?? 0);
    $gcash_reference = $input_data['gcash_reference'] ?? null;
    $points_used = floatval($input_data['points_used'] ?? 0);

    // Get current transaction balance (backward compatible)
    if (isset($has_balance_tracking) && $has_balance_tracking) {
        $balance_sql = "SELECT total_amount, amount_paid, balance FROM transactions WHERE transaction_id = ?";
        $stmt = $conn->prepare($balance_sql);
        $stmt->execute([$transaction_id]);
        $transaction_data = $stmt->fetch(PDO::FETCH_ASSOC);

        $transaction_amount = $transaction_data['total_amount'];
        $current_balance = $transaction_data['balance'];
    } else {
        // Calculate manually for old schema
        $transaction_amount = $grand_total;
        $current_balance = $balance;
    }

    // Validate business rules
    $business_validation = $validator->validateBusinessRules($transaction_id, 'process_payment');
    if (!$business_validation['valid']) {
        $_SESSION['error_message'] = implode(', ', $business_validation['errors']);
        header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }

    // Validate payment data
    $payment_validation = $validator->validatePayment([
        'payment_method' => $payment_method,
        'amount_paid' => $amount_paid,
        'gcash_reference' => $gcash_reference,
        'points_used' => $points_used
    ], $customer_info['customer_id'], $current_balance);

    if (!$payment_validation['valid']) {
        $_SESSION['error_message'] = implode(', ', $payment_validation['errors']);
        header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }

    if ($payment_method === 'points') {
        if ($points_used <= 0) {
            $_SESSION['error_message'] = "Points amount must be greater than 0 for points payment.";
            header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }

        // Validate points redemption
        $validation = $loyaltyManager->validateRedemption($customer_info['customer_id'], $points_used);
        if (!$validation['valid']) {
            $_SESSION['error_message'] = $validation['message'];
            header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }

        // Convert points to payment amount
        $amount_paid = $loyaltyManager->calculatePointsValue($points_used);
    }

    // Validate payment amount using balance tracker
    $validation = $balanceTracker->validatePaymentAmount($transaction_id, $amount_paid);
    if (!$validation['valid']) {
        $_SESSION['error_message'] = $validation['message'];
        header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }

    // Determine actual payment amount (cannot exceed remaining balance)
    $payment_amount = min($amount_paid, $current_balance);

    try {
        $conn->beginTransaction();

        if (isset($has_balance_tracking) && $has_balance_tracking) {
            // Use new balance tracking system
            $payment_data = [
                'method' => $payment_method,
                'transaction_amount' => $transaction_amount,
                'amount_paid' => $payment_amount,
                'gcash_reference' => $gcash_reference,
                'points_used' => $points_used
            ];

            // Debug: Log payment attempt
            error_log("Attempting payment processing for transaction $transaction_id");
            error_log("Payment data: " . json_encode($payment_data));
            error_log("Current balance: $current_balance");

            $payment_success = $balanceTracker->processPayment($transaction_id, $payment_data);

            if (!$payment_success) {
                if ($conn->inTransaction()) {
                    $conn->rollBack();
                }
                error_log("Payment processing failed for transaction $transaction_id");
                $_SESSION['error_message'] = "Payment processing failed. Please check the error logs for details.";
                header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
                exit();
            }

            error_log("Payment processing succeeded for transaction $transaction_id");

            // Handle loyalty points redemption
            if ($points_used > 0) {
                $redemption = $loyaltyManager->redeemPoints($customer_info['customer_id'], $points_used);
                if (!$redemption['valid']) {
                    if ($conn->inTransaction()) {
                        $conn->rollBack();
                    }
                    $_SESSION['error_message'] = "Payment processed but points redemption failed: " . $redemption['message'];
                    header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
                    exit();
                }
            }
        } else {
            // Use old payment processing system
            $payment_sql = "INSERT INTO payments (
                transaction_id, method, transaction_amount,
                amount_paid, gcash_reference_number, points_used
            ) VALUES (?, ?, ?, ?, ?, ?)";

            $stmt = $conn->prepare($payment_sql);
            $stmt->execute([
                $transaction_id,
                $payment_method,
                $transaction_amount,
                $payment_amount,
                $gcash_reference,
                $points_used
            ]);

            // Handle loyalty points redemption (old method)
            if ($points_used > 0) {
                $update_points_sql = "UPDATE loyalty_points
                                    SET status = 'USED',
                                        updated_at = CURRENT_TIMESTAMP
                                    WHERE customer_id = ?
                                    AND status = 'AVAILABLE'
                                    ORDER BY loyalty_id ASC
                                    LIMIT ?";
                $stmt = $conn->prepare($update_points_sql);
                $stmt->execute([$customer_info['customer_id'], $points_used]);
            }
        }

        $conn->commit();
        $_SESSION['success_message'] = "Payment processed successfully!";
        header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
        exit();

    } catch (Exception $e) {
        // Only rollback if a transaction is active
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['error_message'] = "Error processing payment: " . $e->getMessage();
        header("Location: transaction_details.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }
}
// Get available products for add-ons
$products_sql = "SELECT * FROM products WHERE stock_quantity > 0";
$stmt = $conn->prepare($products_sql);
$stmt->execute();
$available_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get pricing categories
$categories_sql = "SELECT * FROM pricing";
$stmt = $conn->prepare($categories_sql);
$stmt->execute();
$pricing_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fresh & Clean Laundry - Transaction Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4A90E2;
            --secondary-color: #67B26F;
            --accent-color: #F7F9FC;
        }
        body { 
            font-family: 'Nunito', sans-serif;
            background: linear-gradient(135deg, var(--accent-color) 0%, #ffffff 100%);
            height: 100vh;
            overflow: hidden;
        }
        .container { 
            height: 100vh;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
        }
        .customer-info {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 15px;
            padding: 1.2rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .points-badge {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        .card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            background: white;
        }
        .card-header {
            background: var(--accent-color);
            border-bottom: 2px solid #eee;
            border-radius: 12px 12px 0 0 !important;
        }
        .btn-primary {
            background: var(--primary-color);
            border: none;
            border-radius: 8px;
        }
        .btn-success {
            background: var(--secondary-color);
            border: none;
            border-radius: 8px;
        }
        .table {
            font-size: 0.9rem;
        }
        .table th {
            font-weight: 600;
            color: var(--primary-color);
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #eee;
        }
        .total-section {
            background: var(--accent-color);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .washing-icon {
            font-size: 1.5rem;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <div class="customer-info">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4><i class="fas fa-user-circle me-2"></i><?php echo htmlspecialchars($customer_info['full_name'] ?? 'Customer'); ?></h4>
                    <p class="mb-0"><i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($customer_info['phone_number'] ?? 'N/A'); ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="points-badge">
                        <i class="fas fa-star"></i>
                        <span>Points: <?php echo number_format($customer_info['points'] ?? 0); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Details Section -->
        <div class="row g-4">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Transaction Items</h5>
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addItemModal">
                            <i class="fas fa-plus me-2"></i>Add Item
                        </button>
                    </div>
                    <div class="card-body">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 25%">Total</th>
                                    <th>Category</th>
                                    <th>Weight (kg)</th>
                                    <th>Price/kg</th>
                                    <th>Subtotal</th>
                                    <th>Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $Total = 0;
                                foreach ($transaction_details as $detail): 
                                    $Total += $detail['subtotal'];
                                ?>
                                <tr>
                                    <td></td>
                                    <td><?php echo htmlspecialchars($detail['category_name']); ?></td>
                                    <td><?php echo number_format($detail['weight'], 2); ?></td>
                                    <td>₱<?php echo number_format($detail['price_per_kg'], 2); ?></td>
                                    <td>₱<?php echo number_format($detail['subtotal'], 2); ?></td>
                                    <td><?php echo htmlspecialchars($detail['item_remarks']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <tr class="table-light">
                                    <td class="fw-bold text-primary">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calculator me-2"></i>
                                            ₱<?php echo number_format($Total, 2); ?>
                                        </div>
                                    </td>
                                    <td colspan="5"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Add-on Products</h5>
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="fas fa-plus me-2"></i>Add Product
                        </button>
                    </div>
                    <div class="card-body">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 25%">Total</th>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $STotal = 0;
                                foreach ($addon_products as $addon): 
                                    $STotal += $addon['trans_subtotal'];
                                ?>
                                <tr>
                                    <td></td>
                                    <td><?php echo htmlspecialchars($addon['name']); ?></td>
                                    <td><?php echo number_format($addon['trans_qty']); ?></td>
                                    <td>₱<?php echo number_format($addon['price'], 2); ?></td>
                                    <td>₱<?php echo number_format($addon['trans_subtotal'], 2); ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <tr class="table-light">
                                    <td class="fw-bold text-primary">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calculator me-2"></i>
                                            ₱<?php echo number_format($STotal, 2); ?>
                                        </div>
                                    </td>
                                    <td colspan="4"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Payment Details</h5>
                    </div>
                        <div class="card-body">
                            <div class="total-section mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Items Total:</span>
                                    <strong>₱<?php echo number_format($total_amount, 2); ?></strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Add-ons Total:</span>
                                    <strong>₱<?php echo number_format($addons_total, 2); ?></strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Grand Total:</span>
                                    <strong id="grandTotal">₱<?php echo number_format($grand_total, 2); ?></strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Amount Paid:</span>
                                    <strong class="text-success">₱<?php echo number_format($amount_paid, 2); ?></strong>
                                </div>
                                <?php if ($balance > 0): ?>
                                <div class="d-flex justify-content-between text-danger">
                                    <span>Remaining Balance:</span>
                                    <strong>₱<?php echo number_format($balance, 2); ?></strong>
                                </div>
                                <?php elseif ($balance < 0): ?>
                                <div class="d-flex justify-content-between text-info">
                                    <span>Overpayment:</span>
                                    <strong>₱<?php echo number_format(abs($balance), 2); ?></strong>
                                </div>
                                <?php else: ?>
                                <div class="d-flex justify-content-between text-success">
                                    <span>Status:</span>
                                    <strong>FULLY PAID</strong>
                                </div>
                                <?php endif; ?>
                            </div>

                            <?php if ($balance > 0): ?>
                            <form method="POST" action="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>" id="paymentForm">
                                <input type="hidden" name="transaction_id" value="<?php echo $transaction_id; ?>">
                                <input type="hidden" id="totalAmount" value="<?php echo $grand_total; ?>">
                                <input type="hidden" id="remainingBalance" value="<?php echo $balance; ?>">

                                <div class="mb-3">
                                    <label class="form-label">Payment Method</label>
                                    <select name="payment_method" id="paymentMethod" class="form-select" required>
                                        <option value="cash">Cash</option>
                                        <option value="gcash">GCash</option>
                                        <option value="credit">Credit</option>
                                        <?php if (!empty($customer_info['points']) && $customer_info['points'] >= 10): ?>
                                            <option value="points">Points</option>
                                        <?php endif; ?>
                                    </select>
                                </div>

                                <div class="mb-3 amount-paid-group">
                                    <label class="form-label">Amount Tendered</label>
                                    <input type="number" step="0.01" name="amount_paid" id="amountTendered" class="form-control" required
                                           oninput="calculateChange()" min="0">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Change</label>
                                    <input type="text" id="changeAmount" class="form-control" readonly>
                                </div>

                                <div class="mb-3 gcash-group" style="display: none;">
                                    <label class="form-label">GCash Reference Number</label>
                                    <input type="text" name="gcash_reference" class="form-control">
                                </div>

                                <?php if (!empty($customer_info['available_points'])): ?>
                                    <div class="mb-3 points-group">
                                        <label class="form-label">Use Points (Available: <?php echo number_format($customer_info['available_points']); ?>)</label>
                                        <input type="number" name="points_used" max="<?php echo $customer_info['available_points']; ?>" class="form-control">
                                    </div>
                                <?php endif; ?>

                                <script>
                                    function calculateChange() {
                                        const amountTendered = parseFloat(document.getElementById('amountTendered').value) || 0;
                                        const remainingBalance = parseFloat(document.getElementById('remainingBalance').value) || 0;
                                        const change = amountTendered - remainingBalance;

                                        if (change >= 0) {
                                            document.getElementById('changeAmount').value = '₱' + change.toFixed(2);
                                        } else {
                                            document.getElementById('changeAmount').value = '₱0.00';
                                        }
                                    }

                                    // Handle payment method changes
                                    document.addEventListener('DOMContentLoaded', function() {
                                        const paymentMethodSelect = document.getElementById('paymentMethod');
                                        if (paymentMethodSelect) {
                                            paymentMethodSelect.addEventListener('change', function() {
                                                const method = this.value;
                                                const amountPaidGroup = document.querySelector('.amount-paid-group');
                                                const gcashGroup = document.querySelector('.gcash-group');
                                                const pointsGroup = document.querySelector('.points-group');

                                                // Show/hide fields based on payment method
                                                if (method === 'points') {
                                                    if (amountPaidGroup) amountPaidGroup.style.display = 'none';
                                                    const amountInput = document.getElementById('amountTendered');
                                                    if (amountInput) amountInput.required = false;
                                                } else {
                                                    if (amountPaidGroup) amountPaidGroup.style.display = 'block';
                                                    const amountInput = document.getElementById('amountTendered');
                                                    if (amountInput) amountInput.required = true;
                                                }

                                                if (method === 'gcash') {
                                                    if (gcashGroup) gcashGroup.style.display = 'block';
                                                } else {
                                                    if (gcashGroup) gcashGroup.style.display = 'none';
                                                }
                                            });
                                        }
                                    });
                                </script>

                            <button type="submit" name="process_payment" class="btn btn-success w-100" id="processPaymentBtn">
                                <i class="fas fa-check-circle me-2"></i>Process Payment
                            </button>
                            </form>
                            <?php else: ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                This transaction is fully paid!
                            </div>
                            <?php endif; ?>

                            <a href="transactions.php?customer_id=<?php echo $customer_info['customer_id']; ?>" class="btn btn-secondary w-100 mt-2">
                                <i class="fas fa-arrow-left me-2"></i>Back to Transactions
                            </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Laundry Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Category</label>
                            <select name="category" class="form-select" required>
                                <?php foreach ($pricing_categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['category']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Weight (kg)</label>
                            <input type="number" step="0.1" name="weight" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Remarks</label>
                            <textarea name="item_remarks" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" name="save_details" class="btn btn-primary">Save Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Product</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Product</label>
                            <select name="product_id" class="form-select" required>
                                <?php foreach ($available_products as $product): ?>
                                <option value="<?php echo $product['product_id']; ?>">
                                    <?php echo htmlspecialchars($product['name']); ?> - 
                                    ₱<?php echo number_format($product['price'], 2); ?> 
                                    (Stock: <?php echo $product['stock_quantity']; ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Quantity</label>
                            <input type="number" name="quantity" class="form-control" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" name="add_product" class="btn btn-primary">Add Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
