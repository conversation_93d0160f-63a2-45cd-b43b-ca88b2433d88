<?php
session_start();
require_once '../../config/database.php';

/**
 * Advanced Reporting System
 * Provides comprehensive business analytics and reporting capabilities
 */

class AdvancedReportGenerator {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Generate comprehensive sales report with analytics
     */
    public function generateSalesReport($startDate, $endDate, $groupBy = 'daily') {
        $groupByClause = $this->getGroupByClause($groupBy);
        $dateFormat = $this->getDateFormat($groupBy);
        
        $sql = "SELECT 
                    $dateFormat as period,
                    COUNT(DISTINCT t.transaction_id) as transaction_count,
                    COUNT(DISTINCT t.customer_id) as unique_customers,
                    SUM(t.total_amount) as total_sales,
                    SUM(t.amount_paid) as total_collected,
                    SUM(t.balance) as outstanding_balance,
                    AVG(t.total_amount) as avg_transaction_value,
                    MIN(t.total_amount) as min_transaction,
                    MAX(t.total_amount) as max_transaction,
                    SUM(CASE WHEN t.status = 'PAID' THEN 1 ELSE 0 END) as paid_transactions,
                    SUM(CASE WHEN t.status = 'PENDING' THEN 1 ELSE 0 END) as pending_transactions,
                    SUM(CASE WHEN t.status = 'PARTIAL' THEN 1 ELSE 0 END) as partial_transactions
                FROM transactions t
                WHERE DATE(t.transaction_date) BETWEEN ? AND ?
                AND t.status != 'CANCELLED'
                GROUP BY $groupByClause
                ORDER BY period";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Generate customer analytics report
     */
    public function generateCustomerAnalytics($startDate, $endDate) {
        $sql = "SELECT 
                    c.customer_id,
                    c.full_name,
                    c.phone_number,
                    c.address,
                    COUNT(t.transaction_id) as total_transactions,
                    SUM(t.total_amount) as total_spent,
                    SUM(t.amount_paid) as total_paid,
                    SUM(t.balance) as outstanding_balance,
                    AVG(t.total_amount) as avg_transaction_value,
                    MIN(t.transaction_date) as first_transaction,
                    MAX(t.transaction_date) as last_transaction,
                    DATEDIFF(MAX(t.transaction_date), MIN(t.transaction_date)) as customer_lifetime_days,
                    COALESCE(lp.points, 0) as loyalty_points,
                    -- Customer segmentation
                    CASE 
                        WHEN SUM(t.total_amount) >= 10000 THEN 'VIP'
                        WHEN SUM(t.total_amount) >= 5000 THEN 'Premium'
                        WHEN SUM(t.total_amount) >= 1000 THEN 'Regular'
                        ELSE 'New'
                    END as customer_segment,
                    -- Recency analysis
                    DATEDIFF(CURDATE(), MAX(t.transaction_date)) as days_since_last_transaction,
                    CASE 
                        WHEN DATEDIFF(CURDATE(), MAX(t.transaction_date)) <= 30 THEN 'Active'
                        WHEN DATEDIFF(CURDATE(), MAX(t.transaction_date)) <= 90 THEN 'At Risk'
                        ELSE 'Inactive'
                    END as customer_status
                FROM customers c
                LEFT JOIN transactions t ON c.customer_id = t.customer_id 
                    AND DATE(t.transaction_date) BETWEEN ? AND ?
                    AND t.status != 'CANCELLED'
                LEFT JOIN loyalty_points lp ON c.customer_id = lp.customer_id 
                    AND lp.status = 'active'
                GROUP BY c.customer_id, c.full_name, c.phone_number, c.address, lp.points
                HAVING total_transactions > 0
                ORDER BY total_spent DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Generate service performance report
     */
    public function generateServicePerformanceReport($startDate, $endDate) {
        $sql = "SELECT 
                    td.category,
                    COUNT(*) as service_count,
                    SUM(td.weight) as total_weight,
                    AVG(td.weight) as avg_weight_per_service,
                    SUM(td.subtotal) as total_revenue,
                    AVG(td.subtotal) as avg_revenue_per_service,
                    AVG(td.price_per_kg) as avg_price_per_kg,
                    MIN(td.price_per_kg) as min_price_per_kg,
                    MAX(td.price_per_kg) as max_price_per_kg,
                    -- Calculate percentage of total revenue
                    (SUM(td.subtotal) / (SELECT SUM(subtotal) FROM transaction_details td2 
                        INNER JOIN transactions t2 ON td2.transaction_id = t2.transaction_id 
                        WHERE DATE(t2.transaction_date) BETWEEN ? AND ?
                        AND t2.status != 'CANCELLED')) * 100 as revenue_percentage
                FROM transaction_details td
                INNER JOIN transactions t ON td.transaction_id = t.transaction_id
                WHERE DATE(t.transaction_date) BETWEEN ? AND ?
                AND t.status != 'CANCELLED'
                GROUP BY td.category
                ORDER BY total_revenue DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$startDate, $endDate, $startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Generate financial summary report
     */
    public function generateFinancialSummary($startDate, $endDate) {
        // Get revenue data
        $revenueSql = "SELECT 
                        SUM(total_amount) as total_revenue,
                        SUM(amount_paid) as total_collected,
                        SUM(balance) as outstanding_receivables,
                        COUNT(*) as total_transactions
                    FROM transactions 
                    WHERE DATE(transaction_date) BETWEEN ? AND ?
                    AND status != 'CANCELLED'";
        
        $stmt = $this->conn->prepare($revenueSql);
        $stmt->execute([$startDate, $endDate]);
        $revenue = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get expense data
        $expenseSql = "SELECT 
                        SUM(amount) as total_expenses,
                        COUNT(*) as expense_count,
                        AVG(amount) as avg_expense
                    FROM expenses 
                    WHERE DATE(expense_date) BETWEEN ? AND ?";
        
        $stmt = $this->conn->prepare($expenseSql);
        $stmt->execute([$startDate, $endDate]);
        $expenses = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Calculate financial metrics
        $netProfit = $revenue['total_collected'] - $expenses['total_expenses'];
        $profitMargin = $revenue['total_collected'] > 0 ? ($netProfit / $revenue['total_collected']) * 100 : 0;
        
        return [
            'revenue' => $revenue,
            'expenses' => $expenses,
            'net_profit' => $netProfit,
            'profit_margin' => $profitMargin,
            'collection_rate' => $revenue['total_revenue'] > 0 ? ($revenue['total_collected'] / $revenue['total_revenue']) * 100 : 0
        ];
    }
    
    /**
     * Generate employee performance report
     */
    public function generateEmployeePerformanceReport($startDate, $endDate) {
        $sql = "SELECT 
                    e.employee_id,
                    e.full_name,
                    e.position,
                    COUNT(das.date) as days_worked,
                    SUM(das.total_hours) as total_hours,
                    AVG(das.total_hours) as avg_hours_per_day,
                    SUM(das.overtime_hours) as total_overtime,
                    -- Calculate attendance rate
                    (COUNT(das.date) / DATEDIFF(?, ?)) * 100 as attendance_rate,
                    -- Get pay information
                    COALESCE(p.daily_rate, 0) as daily_rate,
                    COALESCE(p.daily_rate * COUNT(das.date), 0) as total_basic_pay,
                    -- Calculate deductions
                    COALESCE(SUM(d.deduct_fees), 0) as total_deductions
                FROM employees e
                LEFT JOIN daily_attendance_summary das ON e.employee_id = das.employee_id
                    AND das.date BETWEEN ? AND ?
                LEFT JOIN pay p ON e.employee_id = p.employee_id
                LEFT JOIN deductions d ON e.employee_id = d.employee_id
                    AND STR_TO_DATE(CONCAT(d.deduct_year, '-', 
                        CASE d.deduct_month 
                            WHEN 'January' THEN '01'
                            WHEN 'February' THEN '02'
                            WHEN 'March' THEN '03'
                            WHEN 'April' THEN '04'
                            WHEN 'May' THEN '05'
                            WHEN 'June' THEN '06'
                            WHEN 'July' THEN '07'
                            WHEN 'August' THEN '08'
                            WHEN 'September' THEN '09'
                            WHEN 'October' THEN '10'
                            WHEN 'November' THEN '11'
                            WHEN 'December' THEN '12'
                        END, '-01'), '%Y-%m-%d') BETWEEN ? AND ?
                GROUP BY e.employee_id, e.full_name, e.position, p.daily_rate
                ORDER BY total_hours DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$endDate, $startDate, $startDate, $endDate, $startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Generate payment method analysis
     */
    public function generatePaymentMethodAnalysis($startDate, $endDate) {
        $sql = "SELECT 
                    p.method,
                    COUNT(*) as payment_count,
                    SUM(p.amount_paid) as total_amount,
                    AVG(p.amount_paid) as avg_payment_amount,
                    MIN(p.amount_paid) as min_payment,
                    MAX(p.amount_paid) as max_payment,
                    -- Calculate percentage of total payments
                    (SUM(p.amount_paid) / (SELECT SUM(amount_paid) FROM payments p2 
                        INNER JOIN transactions t2 ON p2.transaction_id = t2.transaction_id 
                        WHERE DATE(t2.transaction_date) BETWEEN ? AND ?)) * 100 as percentage_of_total
                FROM payments p
                INNER JOIN transactions t ON p.transaction_id = t.transaction_id
                WHERE DATE(t.transaction_date) BETWEEN ? AND ?
                GROUP BY p.method
                ORDER BY total_amount DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$startDate, $endDate, $startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Helper method to get GROUP BY clause based on period
     */
    private function getGroupByClause($groupBy) {
        switch($groupBy) {
            case 'hourly':
                return 'DATE(transaction_date), HOUR(transaction_date)';
            case 'daily':
                return 'DATE(transaction_date)';
            case 'weekly':
                return 'YEARWEEK(transaction_date)';
            case 'monthly':
                return 'YEAR(transaction_date), MONTH(transaction_date)';
            case 'yearly':
                return 'YEAR(transaction_date)';
            default:
                return 'DATE(transaction_date)';
        }
    }
    
    /**
     * Helper method to get date format based on period
     */
    private function getDateFormat($groupBy) {
        switch($groupBy) {
            case 'hourly':
                return 'CONCAT(DATE(transaction_date), " ", HOUR(transaction_date), ":00")';
            case 'daily':
                return 'DATE(transaction_date)';
            case 'weekly':
                return 'CONCAT(YEAR(transaction_date), "-W", WEEK(transaction_date))';
            case 'monthly':
                return 'CONCAT(YEAR(transaction_date), "-", LPAD(MONTH(transaction_date), 2, "0"))';
            case 'yearly':
                return 'YEAR(transaction_date)';
            default:
                return 'DATE(transaction_date)';
        }
    }
    
    /**
     * Export data to CSV format
     */
    public function exportToCSV($data, $filename, $headers = null) {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // Write headers
        if ($headers) {
            fputcsv($output, $headers);
        } elseif (!empty($data)) {
            fputcsv($output, array_keys($data[0]));
        }
        
        // Write data
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit;
    }
}
?>
