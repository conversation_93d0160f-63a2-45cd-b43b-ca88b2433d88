<?php
include_once '../config/database.php';


// Get all employees for the dropdown
$sql = "SELECT employee_id, full_name FROM employees";
$stmt = $conn->prepare($sql);
$stmt->execute();
$employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get expenses records for selected employee and date range
$selected_employee = isset($_GET['employee_id']) ? $_GET['employee_id'] : '';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

$expense_records = [];
$total_expenses = 0;

if ($start_date && $end_date) {
    // Get expense records
    $sql = "SELECT 
                DATE(e.created_at) as expense_date,
                e.description,
                e.amount,
                emp.full_name as recorded_by
            FROM expenses e
            INNER JOIN employees emp ON e.recorded_by = emp.employee_id 
            WHERE DATE(e.created_at) BETWEEN ? AND ?
            ORDER BY DATE(e.created_at)";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$start_date, $end_date]);
    $expense_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate total expenses
    $sql = "SELECT SUM(amount) as total 
            FROM expenses 
            WHERE DATE(created_at) BETWEEN ? AND ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$start_date, $end_date]);
    $total_expenses = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Monthly Expenses</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <!-- Report Header Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h2 class="card-title h4 mb-4">
                    <i class="fa-solid fa-money-bill me-2"></i>Monthly Expenses Report
                </h2>
                
                <!-- Employee Selection and Date Range Form -->
                <form method="GET" class="needs-validation" novalidate>
                    <div class="row g-3 align-items-end">
                        <div class="col-md-5">
                            <label class="form-label small text-muted">Start Date</label>
                            <input type="date" 
                                   name="start_date" 
                                   class="form-control form-control-lg shadow-sm" 
                                   value="<?php echo $start_date; ?>" 
                                   required>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label small text-muted">End Date</label>
                            <input type="date" 
                                   name="end_date" 
                                   class="form-control form-control-lg shadow-sm" 
                                   value="<?php echo $end_date; ?>" 
                                   required>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary btn-lg w-100 mb-2 shadow-sm">
                                <i class="fa-solid fa-search me-2"></i>View Expenses
                            </button>
                            <?php
                            $filename = 'expense_report_' . date('MY', strtotime($start_date));
                            ?>
                            <a href="generate_expense_pdf.php?start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>&filename=<?php echo $filename; ?>" 
                                class="btn btn-outline-danger btn-lg w-100 shadow-sm"
                                target="_blank">
                                 <i class="fa-solid fa-file-pdf me-2"></i>Export PDF
                            </a>
                            <a href="../pages/reports.php" class="btn btn-secondary btn-lg w-100 shadow-sm mt-2">
                                <i class="fa-solid fa-arrow-left me-2"></i>Back
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Expenses Records Table -->
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Description</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($expense_records as $record): ?>
                        <tr>
                            <td><?php echo date('M d, Y', strtotime($record['expense_date'])); ?></td>
                            <td><?php echo htmlspecialchars($record['description']); ?></td>
                            <td>₱<?php echo number_format($record['amount'], 2); ?></td>
                        </tr>
                    <?php endforeach; ?>
                    
                    <?php if (!empty($expense_records)): ?>
                        <tr class="table-primary fw-bold">
                            <td colspan="2" class="text-end">Total Expenses:</td>
                            <td>₱<?php echo number_format($total_expenses, 2); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
