<?php
session_start();
include_once '../config/database.php';

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $cat_id = $_POST['cat_id'];
        $kilogram = $_POST['kilogram'];
        $num_load = $_POST['num_load'];
        $price = $_POST['price'];
        
        $sql = "INSERT INTO princing_detail (cat_id, kilogram, num_load, price) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$cat_id, $kilogram, $num_load, $price]);
    }
}

if (isset($_POST['update'])) {
    $id = isset($_POST['pricing_id']) ? intval($_POST['pricing_id']) : 0;  // Changed from 'id' to 'pricing_id'
    $cat_id = isset($_POST['cat_id']) ? intval($_POST['cat_id']) : 0;
    $kilogram = isset($_POST['kilogram']) ? floatval($_POST['kilogram']) : 0;
    $num_load = isset($_POST['num_load']) ? intval($_POST['num_load']) : 0;
    $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
    
    if ($id > 0) {
        $sql = "UPDATE princing_detail SET cat_id=?, kilogram=?, num_load=?, price=? WHERE pricing_id=?";
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([$cat_id, $kilogram, $num_load, $price, $id]);
        
        if (!$result) {
            error_log("Update failed for ID: $id, cat_id: $cat_id");
            error_log(print_r($stmt->errorInfo(), true));
        }
    }
}

if (isset($_POST['delete']) && isset($_POST['pricing_id'])) {  // Changed from 'id' to 'pricing_id'
    $id = intval($_POST['pricing_id']);
    $sql = "DELETE FROM princing_detail WHERE pricing_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
}

// Fetch categories from pricing table
$sql = "SELECT * FROM pricing";
$stmt = $conn->prepare($sql);
$stmt->execute();
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch pricing details data
$sql = "SELECT pd.*, p.category, COALESCE(pd.price, 0) as price 
        FROM princing_detail pd 
        JOIN pricing p ON pd.cat_id = p.id";
$stmt = $conn->prepare($sql);
$stmt->execute();
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Pricing Details Management</title>
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; }
        .table thead th { background-color: #0d6efd; color: white; }
        .input-group { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Product/Services Management</h2>

        <!-- Add/Edit Pricing Details Form -->
        <form method="POST" class="mb-4">
            <!-- Changed from 'id' to 'pricing_id' -->
            <input type="hidden" name="pricing_id" id="pricing_id">
            <div class="row mb-3">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-tag"></i></span>
                        <select name="cat_id" class="form-control" required>
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['category']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-weight-scale"></i></span>
                        <input type="number" step="0.1" name="kilogram" class="form-control" placeholder="Kilogram" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-spinner"></i></span>
                        <input type="number" name="num_load" class="form-control" placeholder="Number of Loads" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text">&#x20B1;</span>
                        <input type="number" step="0.01" name="price" class="form-control" placeholder="Price" required>
                    </div>
                </div>
                <div class="col">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="submit" name="save" class="btn btn-success">
                            <i class="fas fa-save"></i> Save New
                        </button>
                        <button type="submit" name="update" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Update
                        </button>
                        <a href="../index.php" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Home
                        </a>
                        <a href="pricing.php" class="btn btn-info">
                            <i class="fas fa-plus"></i> Add Category
                        </a>
                    </div>
                </div>
            </div>
        </form>

        <!-- Pricing Details Table -->
        <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-striped">
                <thead style="position: sticky; top: 0; background: white;">
                    <tr>
                        <th>ID</th>
                        <th>Category</th>
                        <th>Kilogram</th>
                        <th>Number of Loads</th>
                        <th>Price</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($result as $row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['pricing_id']); ?></td>
                        <td><?php echo htmlspecialchars($row['category']); ?></td>
                        <td><?php echo htmlspecialchars($row['kilogram']); ?></td>
                        <td><?php echo htmlspecialchars($row['num_load']); ?></td>
                        <td><?php echo htmlspecialchars(number_format((float)$row['price'], 2)); ?></td>
                        <td>
                            <button onclick="editPricingDetails(<?php echo htmlspecialchars(json_encode($row)); ?>)" 
                                    class="btn btn-sm btn-primary">Edit</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function editPricingDetails(pricing) {
        document.querySelector('[name="pricing_id"]').value = pricing.pricing_id;
        document.querySelector('[name="cat_id"]').value = pricing.cat_id;
        document.querySelector('[name="kilogram"]').value = pricing.kilogram;
        document.querySelector('[name="num_load"]').value = pricing.num_load;
        document.querySelector('[name="price"]').value = pricing.price;
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
