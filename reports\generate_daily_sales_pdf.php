<?php
session_start();
require('../fpdf/fpdf.php');
include('../config/database.php');



// Fetch business profile
$sql = "SELECT * FROM profile";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single row

// Ensure business_name is retrieved correctly
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad"; 
$business_owner = $profile['business_owner'] ??""; 
$business_address = $profile['business_address'] ??""; 
$business_cell = $profile['business_cell'] ??""; 
$business_land = $profile['business_land'] ??""; 

if(!isset($_SESSION['employee_id'])) {
    header('Location: ../index.php');
    exit();
}

// Custom PDF class remains the same
// Custom PDF class with header and footer
class PDF extends FPDF {
    function Header() {
        // Logo (replace path with your logo)
        // $this->Image('logo.png', 10, 10, 30);
        
        // Company name
        $this->SetFont('Arial', 'B', 18);
        $this->Cell(0, 10, $GLOBALS['business_name'], 0, 1, 'C');
        
        // Address
        $this->SetFont('Arial', '', 8);
        $this->Cell(0, 5, $GLOBALS['business_address'], 0, 1, 'C');
        $this->Cell(0, 5, 'Phone: ' . $GLOBALS['business_cell'] . ' | Landline: ' . $GLOBALS['business_land'] . ' ', 0, 1, 'C');
        $this->Cell(0, 5, 'Owed and Managed by: ' . $GLOBALS['business_owner'] .  ' ', 0, 1, 'C');
        
        // Line break
        $this->Ln(10);
    }
    
    function Footer() {
        $this->SetY(-15);
        $this->SetFont('Arial', 'I', 8);
        $this->Cell(0, 10, 'Page '.$this->PageNo().'/{nb}', 0, 0, 'C');
    }
}


    if(isset($_GET['selected_date'])) {
        $selected_date = $_GET['selected_date'];
        
        $pdf = new PDF('P', 'mm', 'Letter');
        $pdf->AliasNbPages();
        $pdf->AddPage();
        
        // Add report date and title after AddPage()
    $pdf->SetFont('Arial', 'B', 14);
    $pdf->Cell(0, 10, 'Daily Sales Report', 0, 1, 'C');
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(0, 5, 'Date: ' . date('F d, Y', strtotime($selected_date)), 0, 1, 'C');

    // Updated Table header with Payment Method
    $pdf->Ln(10);
    $pdf->SetFillColor(50, 50, 50);
    $pdf->SetTextColor(255, 255, 255);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(45, 8, 'Customer Name', 1, 0, 'C', true);
    $pdf->Cell(25, 8, 'Trans. ID', 1, 0, 'C', true);
    $pdf->Cell(30, 8, 'Category', 1, 0, 'C', true);
    $pdf->Cell(25, 8, 'Weight', 1, 0, 'C', true);
    $pdf->Cell(35, 8, 'Payment Type', 1, 0, 'C', true);
    $pdf->Cell(35, 8, 'Amount', 1, 1, 'C', true);

    // Reset text color for data
    $pdf->SetTextColor(0, 0, 0);
    $pdf->SetFont('Arial', '', 9);

    // Updated SQL queries for sales records and total
if(isset($_GET['selected_date'])) {
    $selected_date = $_GET['selected_date'];
    
    // Get sales records
    $sql = "SELECT 
                c.full_name,
                td.transaction_id,
                td.category,
                td.weight,
                CASE 
                    WHEN p.method = 'loyalty' THEN -td.subtotal 
                    ELSE td.subtotal 
                END as subtotal,
                t.transaction_date,
                p.method as payment_method
            FROM transaction_details td
            INNER JOIN transactions t ON td.transaction_id = t.transaction_id
            INNER JOIN customers c ON t.customer_id = c.customer_id
            LEFT JOIN payments p ON td.transaction_id = p.transaction_id
            WHERE DATE(t.transaction_date) = ?
            ORDER BY c.full_name, td.transaction_id";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$selected_date]);
    $sales_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get total sales (excluding loyalty payments)
    $sql_total = "SELECT SUM(
                    CASE 
                        WHEN p.method = 'loyalty' THEN 0
                        ELSE td.subtotal 
                    END
                ) as total 
                FROM transaction_details td
                INNER JOIN transactions t ON td.transaction_id = t.transaction_id
                LEFT JOIN payments p ON td.transaction_id = p.transaction_id
                WHERE DATE(t.transaction_date) = ?";
    
    $stmt = $conn->prepare($sql_total);
    $stmt->execute([$selected_date]);
    $total_sales = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
}


    // Add this after setting the text color back to black and before the total sales calculation
    foreach($sales_records as $record) {
        $pdf->Cell(45, 8, $record['full_name'], 1, 0, 'L');
        $pdf->Cell(25, 8, $record['transaction_id'], 1, 0, 'C');
        $pdf->Cell(30, 8, $record['category'], 1, 0, 'C');
        $pdf->Cell(25, 8, $record['weight'] . ' kg', 1, 0, 'R');
        $pdf->Cell(35, 8, ucfirst($record['payment_method']), 1, 0, 'C');
        $pdf->Cell(35, 8, 'PHP ' . number_format($record['subtotal'], 2), 1, 1, 'R');
    }


    // Total sales
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(160, 8, 'Total Sales:', 1, 0, 'R');
    $pdf->Cell(35, 8, 'PHP '.number_format($total_sales, 2), 1, 1, 'R');

       // Signature section
    $pdf->Ln(20);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(95, 5, 'Prepared by:', 0, 0);
    $pdf->Cell(95, 5, 'Approved by:', 0, 1);

    $pdf->Ln(15);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(95, 5, '_____________________', 0, 0, 'C');
    $pdf->Cell(95, 5, '_____________________', 0, 1, 'C');
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(95, 5, 'Finance Officer', 0, 0, 'C');
    $pdf->Cell(95, 5, 'KENNETH LEONIDA', 0, 1, 'C');
    $pdf->Cell(95, 5, '', 0, 0, 'C');
    $pdf->Cell(95, 5, 'Owner', 0, 1, 'C');

    // Output PDF
    $filename = isset($_GET['filename']) ? $_GET['filename'] : 'daily sales_report';
    $pdf->Output($filename.'.pdf', 'I');
    exit();
}
?>
