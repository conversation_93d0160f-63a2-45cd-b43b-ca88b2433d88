<?php
// Authentication functions
function login($email, $password, $conn) {
  $query = "SELECT * FROM users WHERE email = ?";
  $stmt = $conn->prepare($query);
  $stmt->bind_param("s", $email);
  $stmt->execute();
  $result = $stmt->get_result();
  
  if ($result->num_rows === 1) {
    $user = $result->fetch_assoc();
    if (password_verify($password, $user['password'])) {
      $_SESSION['user_id'] = $user['id'];
      $_SESSION['user_name'] = $user['name'];
      $_SESSION['user_role'] = $user['role'];
      return true;
    }
  }
  return false;
}

// Database helper functions
function sanitize_input($data) {
  $data = trim($data);
  $data = stripslashes($data);
  $data = htmlspecialchars($data);
  return $data;
}

function get_user_by_id($user_id, $conn) {
  $query = "SELECT * FROM users WHERE id = ?";
  $stmt = $conn->prepare($query);
  $stmt->bind_param("i", $user_id);
  $stmt->execute();
  return $stmt->get_result()->fetch_assoc();
}

// Error handling functions
function display_error($message) {
  return "<div class='alert alert-danger' role='alert'>{$message}</div>";
}

function display_success($message) {
  return "<div class='alert alert-success' role='alert'>{$message}</div>";
}