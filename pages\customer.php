<?php
// Database connection parameters
include_once '../config/database.php'; 

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $full_name = $_POST['full_name'];
        $phone_number = $_POST['phone_number'];
        $address = $_POST['address'];
        
        $sql = "INSERT INTO customers (full_name, phone_number, address, created_at) 
                VALUES (?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$full_name, $phone_number, $address]);
    }
}

if (isset($_POST['update']) && isset($_POST['customer_id'])) {
    $customer_id = $_POST['customer_id'];
    $full_name = $_POST['full_name'];
    $phone_number = $_POST['phone_number'];
    $address = $_POST['address'];
    
    $sql = "UPDATE customers SET full_name=?, phone_number=?, address=? WHERE customer_id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$full_name, $phone_number, $address, $customer_id]);
}

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT * FROM customers WHERE full_name LIKE ? OR phone_number LIKE ?";
$search_term = "%$search%";
$stmt = $conn->prepare($sql);
$stmt->execute([$search_term, $search_term]);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Customer Management</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; }
        .table thead th { background-color: #0d6efd; color: white; }
        .input-group { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Customer Management</h2>
        
        <!-- Search Form -->
        <form method="GET" class="mb-4">
            <div class="input-group">
                <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                <input type="text" name="search" class="form-control" placeholder="Search cusromers..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-primary"><i class="fa-solid fa-search"></i> Search</button>
            </div>
        </form>

        <!-- Modified Add/Edit Customer Form -->
        <form method="POST" class="mb-4">
            <input type="hidden" name="customer_id" id="customer_id">
            <div class="row mb-3">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-user"></i></span>
                        <input type="text" name="full_name" class="form-control" placeholder="Full Name" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-phone"></i></span>
                        <input type="tel" name="phone_number" class="form-control" placeholder="Phone Number" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-location-dot"></i></span>
                        <input type="text" name="address" class="form-control" placeholder="Address" required>
                    </div>
                </div>
                <div class="col">
                    <div class="btn-group" role="group">
                        <button type="submit" name="save" class="btn btn-success"><i class="fa-solid fa-plus"></i> Save New</button>
                        <button type="submit" name="update" class="btn btn-warning"><i class="fa-solid fa-pen"></i> Update</button>
                        <a href="../index.php" class="btn btn-secondary"><i class="fa-solid fa-home"></i> Home</a>
                    </div>
                </div>
            </div>
        </form>

        <!-- Modified Customers Table -->
        <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-striped">
                <thead style="position: sticky; top: 0; background: white;">
                    <tr>
                        <th><i class="fa-solid fa-id-card"></i> ID</th>
                        <th><i class="fa-solid fa-user"></i> Full Name</th>
                        <th><i class="fa-solid fa-phone"></i> Phone Number</th>
                        <th><i class="fa-solid fa-location-dot"></i> Address</th>
                        <th><i class="fa-solid fa-calendar-days"></i> Created At</th>
                        <th><i class="fa-solid fa-gear"></i> Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($result as $row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['customer_id']); ?></td>
                        <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($row['phone_number']); ?></td>
                        <td><?php echo htmlspecialchars($row['address']); ?></td>
                        <td><?php echo htmlspecialchars($row['created_at']); ?></td>
                        <td>
                            <button onclick="editCustomer(<?php echo htmlspecialchars(json_encode($row)); ?>)" class="btn btn-sm btn-primary">Edit</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function editCustomer(customer) {
        document.querySelector('[name="customer_id"]').value = customer.customer_id;
        document.querySelector('[name="full_name"]').value = customer.full_name;
        document.querySelector('[name="phone_number"]').value = customer.phone_number;
        document.querySelector('[name="address"]').value = customer.address;
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
