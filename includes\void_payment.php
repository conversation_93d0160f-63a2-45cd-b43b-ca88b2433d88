<?php
session_start();
include_once '../config/database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if payment_id and transaction_id are set
    if (isset($_POST['payment_id']) && isset($_POST['transaction_id'])) {
        
        // If password is not yet submitted, show the password prompt
        if (!isset($_POST['admin_password'])) {
            ?>
            <!DOCTYPE html>
            <html>
            <head>
                <title>Verify Administrator</title>
                <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
            </head>
            <body>
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Administrator Verification</h4>
                                </div>
                                <div class="card-body">
                                    <form method="POST">
                                        <input type="hidden" name="payment_id" value="<?php echo $_POST['payment_id']; ?>">
                                        <input type="hidden" name="transaction_id" value="<?php echo $_POST['transaction_id']; ?>">
                                        <div class="mb-3">
                                            <label for="admin_password" class="form-label">Enter Administrator Password</label>
                                            <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Verify</button>
                                        <a href="javascript:history.back()" class="btn btn-secondary">Cancel</a>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            <?php
            exit;
        }

        // Verify administrator password
        $password = $_POST['admin_password'];
        $stmt = $conn->prepare("SELECT * FROM users WHERE type = 'Administrator' AND password = ?");
        $stmt->execute([md5($password)]);
        $admin = $stmt->fetch();

        if (!$admin) {
            $_SESSION['error'] = "Invalid administrator password!";
            header("Location: " . $_SERVER['HTTP_REFERER']);
            exit;
        }

        // If password is correct, proceed with payment deletion
        $payment_id = $_POST['payment_id'];
        $transaction_id = $_POST['transaction_id'];

        try {   
            $conn->beginTransaction();

            // Get payment details before deletion
            $stmt = $conn->prepare("SELECT * FROM payments WHERE payment_id = ?");
            $stmt->execute([$payment_id]);
            $payment = $stmt->fetch();

            if ($payment) {
                // Update transaction balance
                $stmt = $conn->prepare("UPDATE transactions SET balance = balance + ? WHERE transaction_id = ?");
                $stmt->execute([$payment['amount_paid'], $transaction_id]);

                // Delete the payment
                $stmt = $conn->prepare("DELETE FROM payments WHERE payment_id = ?");
                $stmt->execute([$payment_id]);

                // If points were used, restore them
                if ($payment['points_used'] > 0) {
                    $stmt = $conn->prepare("UPDATE users SET points = points + ? WHERE user_id = ?");
                    $stmt->execute([$payment['points_used'], $payment['user_id']]);
                }

                $conn->commit();
                $_SESSION['success'] = "Payment has been successfully voided.";
            } else {
                throw new Exception("Payment not found.");
            }

        } catch (Exception $e) {
            $conn->rollBack();
            $_SESSION['error'] = "Error voiding payment: " . $e->getMessage();
        }

        header("Location: " . $_SERVER['HTTP_REFERER']);
        exit;
    }
}

// If accessed directly without POST data, redirect back
header("Location: ../pages/payment.php");
exit;
?>