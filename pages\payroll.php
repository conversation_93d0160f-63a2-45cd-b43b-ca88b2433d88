<?php
include '../config/database.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payroll System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Theme Variables */
        :root {
            --primary-color: #1a237e;
            --secondary-color: #283593;
            --accent-color: #3949ab;
            --success-color: #2e7d32;
            --warning-color: #f57f17;
            --danger-color: #c62828;
            --background-color: #f8f9fa;
            --text-color: #212121;
            --border-radius: 0.5rem;
            --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
            --transition: all 0.25s ease-in-out;
        }

        /* Base Styles */
        body {
            background-color: var(--background-color);
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            color: var(--text-color);
            line-height: 1.5;
        }

        /* Layout */
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        /* Card Styles */
        .card {
            background: #ffffff;
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            margin-bottom: 1.5rem;
        }

        .card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: #ffffff;
            padding: 1.25rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Form Elements */
        .form-select, .form-control {
            border-radius: var(--border-radius);
            padding: 0.75rem;
            border: 1px solid #dee2e6;
            transition: var(--transition);
        }

        .form-select:focus, .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(57, 73, 171, 0.25);
        }

        /* Buttons */
        .btn {
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-primary {
            background-color: var(--accent-color);
            border: none;
        }

        .btn-primary:hover {
            background-color: var(--primary-color);
            transform: translateY(-1px);
        }

        /* Tables */
        .table {
            margin-bottom: 0;
        }

        .table th, .table td {
            padding: 1rem;
            vertical-align: middle;
        }

        .table-primary {
            background-color: rgba(57, 73, 171, 0.1);
        }

        /* Utilities */
        .text-end {
            font-weight: 600;
            color: var(--text-color);
        }

        .alert {
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            border: none;
        }

        /* Icons */
        .fas {
            margin-right: 0.5rem;
            color: inherit;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2>Payroll Computation</h2>
        <form method="POST" class="mb-4">
            <div class="row">
                <div class="col-md-4">
                    <label for="employee" class="form-label">Select Employee</label>
                    <select name="employee" class="form-select" required>
                        <option value="">Choose employee...</option>
                        <?php
                        $query = "SELECT DISTINCT e.employee_id, e.full_name
                                 FROM employees e 
                                 JOIN daily_attendance_summary das ON e.employee_id = das.employee_id";
                        $stmt = $conn->query($query);
                        while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                            echo "<option value='" . $row['employee_id'] . "'>" . 
                                 $row['full_name'] . "</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="year" class="form-label">Year</label>
                    <select name="year" class="form-select" required>
                        <?php
                        $current_year = date('Y');
                        for($i = $current_year - 2; $i <= $current_year + 2; $i++) {
                            $selected = ($i == $current_year) ? 'selected' : '';
                            echo "<option value='$i' $selected>$i</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="month" class="form-label">Month</label>
                    <select name="month" class="form-select" required>
                        <?php
                        $current_month = date('n');
                        for($i = 1; $i <= 12; $i++) {
                            $selected = ($i == ($current_month - 1) || ($current_month == 1 && $i == 12)) ? 'selected' : '';
                            echo "<option value='$i' $selected>" . date('F', mktime(0, 0, 0, $i, 1)) . "</option>";
                        }
                        ?>
                    </select>
                </div>
            </div>
            <div class="d-flex justify-content-start gap-2 mt-3">
                <button type="submit" class="btn btn-primary px-4 py-2">
                    <i class="fas fa-calculator me-2"></i>Generate Payroll
                </button>
                <a href="../reports/payroll_section.php" class="btn btn-secondary"><i class="fa-solid fa-home"></i> Payroll Report Home</a>
                <?php if($_SERVER['REQUEST_METHOD'] == 'POST'): 
                    $employee_id = $_POST['employee'];
                    $year = intval($_POST['year']);
                    $month = intval($_POST['month']);
                ?>
                    <a href="../reports/payroll_pdf.php?employee_id=<?php echo $employee_id; ?>&year=<?php echo $year; ?>&month=<?php echo $month; ?>" 
                        class="btn btn-outline-danger px-4 py-2"
                        target="_blank">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </a>
                <?php endif; ?>
            </div>
        </form>
        <?php
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            $employee_id = $_POST['employee'];
            $year = $_POST['year'];
            $month = $_POST['month'];

            $query = "SELECT 
                monthly_data.employee_id,
                monthly_data.monthly_total_hours,
                monthly_data.total_overtime,
                FORMAT((monthly_data.monthly_total_hours * (monthly_data.daily_rate / 8)), 2) AS monthly_total_pay,
                FORMAT(COALESCE(deductions_data.total_deductions, 0), 2) AS total_deductions,
                FORMAT(((monthly_data.monthly_total_hours * (monthly_data.daily_rate / 8)) - COALESCE(deductions_data.total_deductions, 0)), 2) AS monthly_final_pay,
                e.full_name
            FROM (
                SELECT 
                    das.employee_id,
                    SUM(das.total_hours) AS monthly_total_hours,
                    SUM(das.overtime_hours) AS total_overtime,
                    MAX(p.daily_rate) AS daily_rate
                FROM daily_attendance_summary das
                JOIN pay p ON p.employee_id = das.employee_id
                WHERE das.employee_id = :employee_id
                AND YEAR(das.date) = :year
                AND MONTH(das.date) = :month
                GROUP BY das.employee_id
            ) monthly_data
            LEFT JOIN (
                SELECT employee_id, COALESCE(SUM(deduct_fees), 0) AS total_deductions
                FROM deductions
                WHERE deduct_month = :month
                AND deduct_year = :year
                GROUP BY employee_id
            ) deductions_data ON monthly_data.employee_id = deductions_data.employee_id
            JOIN employees e ON e.employee_id = monthly_data.employee_id";

            $stmt = $conn->prepare($query);
            $stmt->bindParam(':employee_id', $employee_id, PDO::PARAM_INT);
            $stmt->bindParam(':year', $year, PDO::PARAM_INT);
            $stmt->bindParam(':month', $month, PDO::PARAM_INT);
            $stmt->execute();
            $payroll = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get deduction details
            $deduction_query = "SELECT deduct_des, deduct_fees 
                              FROM deductions 
                              WHERE employee_id = :employee_id 
                              AND deduct_year = :year 
                              AND deduct_month = :month";
            $deduction_stmt = $conn->prepare($deduction_query);
            $deduction_stmt->bindParam(':employee_id', $employee_id, PDO::PARAM_INT);
            $deduction_stmt->bindParam(':year', $year, PDO::PARAM_INT);
            $deduction_stmt->bindParam(':month', $month, PDO::PARAM_INT);
            $deduction_stmt->execute();
            $deductions = $deduction_stmt->fetchAll(PDO::FETCH_ASSOC);

            if($payroll) {
                ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h3>Payroll Details for <?php echo $payroll['full_name'] ?></h3>
                        <h5>Period: <?php echo date('F', mktime(0, 0, 0, $month, 1)) . " " . $year; ?></h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <div class="card bg-light mb-3">
                                <div class="card-header">Time Summary</div>
                                <div class="card-body">
                                    <tr class="table-light">
                                        <td><i class="fas fa-clock"></i> Regular Monthly Hours:</td>
                                        <td class="text-end"><strong><?php echo ($payroll['monthly_total_hours'] - $payroll['total_overtime']); ?></strong> hours</td>
                                    </tr>
                                    <tr class="table-light">
                                        <td><i class="fas fa-business-time"></i> Overtime Hours:</td>
                                        <td class="text-end"><strong><?php echo $payroll['total_overtime']; ?></strong> hours</td>
                                    </tr>
                                    <tr class="table-light">
                                        <td><i class="fas fa-hourglass-half"></i> Total Hours Worked:</td>
                                        <td class="text-end"><strong><?php echo $payroll['monthly_total_hours']; ?></strong> hours</td>
                                    </tr>
                                </div>
                            </div>
                            <tr>
                                <td>Gross Pay:</td>
                                <td>₱<?php echo $payroll['monthly_total_pay']; ?></td>
                            </tr>
                            <?php if (!empty($deductions)): ?>
                            <tr>
                                <td colspan="2">
                                    <div class="card bg-light mb-3">
                                        <div class="card-header">Deduction Details</div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Description</th>
                                                        <th class="text-end">Amount</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($deductions as $deduction): ?>
                                                    <tr>
                                                        <td><?php echo $deduction['deduct_des']; ?></td>
                                                        <td class="text-end">₱<?php echo number_format($deduction['deduct_fees'], 2); ?></td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <td>Total Deductions:</td>
                                <td>₱<?php echo $payroll['total_deductions']; ?></td>
                            </tr>
                            <tr class="table-primary">
                                <td><strong>Net Pay:</strong></td>
                                <td><strong>₱<?php echo $payroll['monthly_final_pay']; ?></strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <?php
            } else {
                echo "<div class='alert alert-warning mt-4'>No payroll data found for the selected period.</div>";
            }
        }
        ?>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
