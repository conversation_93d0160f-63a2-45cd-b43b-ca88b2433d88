<?php
include_once '../config/database.php';

// Get receivables records for selected date range
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

$receivable_records = [];
$total_receivables = 0;

if ($start_date && $end_date) {
    // Get daily receivables records
    $sql = "SELECT payments.payment_id, 
           payments.transaction_id, 
           payments.balance, 
           DATE_FORMAT(transactions.transaction_date, '%M %d, %Y') AS formatted_date
    FROM payments
    INNER JOIN transactions ON transactions.transaction_id = payments.transaction_id
    WHERE payments.balance > 0
    AND payments.payment_id = (
        SELECT MAX(p.payment_id) 
        FROM payments p 
        WHERE p.transaction_id = payments.transaction_id
    )
    ORDER BY payments.transaction_id ASC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $receivable_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate total receivables
    $sql = "SELECT 
        SUM(p.transaction_amount - p.amount_paid) as total
    FROM payments p
    JOIN transactions t ON p.transaction_id = t.transaction_id
    JOIN customers c ON t.customer_id = c.customer_id
    WHERE DATE(t.transaction_date) BETWEEN ? AND ?
        AND p.transaction_amount > p.amount_paid";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$start_date, $end_date]);
    $total_receivables = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Monthly Receivables</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <!-- Report Header Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h2 class="card-title h4 mb-4">
                    <i class="fa-solid fa-money-bill-transfer me-2"></i>Monthly Receivables Report
                </h2>
                
                <!-- Date Range Form -->
                <form method="GET" class="needs-validation" novalidate>
                    <div class="row g-3 align-items-end">
                        <div class="col-md-5">
                            <label class="form-label small text-muted">Start Date</label>
                            <input type="date" name="start_date" class="form-control form-control-lg shadow-sm" 
                                   value="<?php echo $start_date; ?>" required>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label small text-muted">End Date</label>
                            <input type="date" name="end_date" class="form-control form-control-lg shadow-sm" 
                                   value="<?php echo $end_date; ?>" required>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary btn-lg w-100 mb-2 shadow-sm">
                                <i class="fa-solid fa-search me-2"></i>View Receivables
                            </button>
                            <?php
                            $filename = 'monthly_receivables_' . date('MY', strtotime($start_date));
                            ?>
                            <a href="generate_receivables_pdf.php?start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>&filename=<?php echo $filename; ?>" 
                                class="btn btn-outline-danger btn-lg w-100 mb-2 shadow-sm"
                                target="_blank">
                                <i class="fa-solid fa-file-pdf me-2"></i>Export PDF
                            </a>
                            <a href="../pages/reports.php" class="btn btn-secondary btn-lg w-100 shadow-sm">
                                <i class="fa-solid fa-arrow-left me-2"></i>Back
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Receivables Records Table -->
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Date</th>
                        <th class="text-end">Outstanding Balance</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($receivable_records as $record): ?>
                        <tr>
                            <td><?php echo $record['formatted_date']; ?></td>
                            <td class="text-end">₱<?php echo number_format($record['balance'], 2); ?></td>
                        </tr>
                    <?php endforeach; ?>
                    
                    <?php 
                    if (!empty($receivable_records)): 
                        $total = 0;
                        foreach ($receivable_records as $record) {
                            $total += $record['balance'];
                        }
                    ?>
                        <tr class="table-primary fw-bold">
                            <td colspan="1" class="text-end">Total Receivables:</td>
                            <td class="text-end">₱<?php echo number_format($total, 2); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <?php if (empty($receivable_records)): ?>
            <div class="alert alert-info text-center">
                <i class="fa-solid fa-info-circle me-2"></i>No receivables found for the selected date range.
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
