<?php
session_start();
require('../fpdf/fpdf.php');
include('../config/database.php');

if(!isset($_SESSION['employee_id'])) {
    header('Location: ../index.php');
    exit();
}


// Fetch business profile
$sql = "SELECT * FROM profile";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single row

// Ensure business_name is retrieved correctly
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad"; 
$business_owner = $profile['business_owner'] ??""; 
$business_address = $profile['business_address'] ??""; 
$business_cell = $profile['business_cell'] ??""; 
$business_land = $profile['business_land'] ??""; 

// Custom PDF class with header and footer
class PDF extends FPDF {
   function Header() {
        // Logo (replace path with your logo)
        // $this->Image('logo.png', 10, 10, 30);
        
        // Company name
        $this->SetFont('Arial', 'B', 18);
        $this->Cell(0, 10, $GLOBALS['business_name'], 0, 1, 'C');
        
        // Address
        $this->SetFont('Arial', '', 8);
        $this->Cell(0, 5, $GLOBALS['business_address'], 0, 1, 'C');
        $this->Cell(0, 5, 'Phone: ' . $GLOBALS['business_cell'] . ' | Landline: ' . $GLOBALS['business_land'] . ' ', 0, 1, 'C');
        $this->Cell(0, 5, 'Owed and Managed by: ' . $GLOBALS['business_owner'] .  ' ', 0, 1, 'C');
        
        // Line break
        $this->Ln(10);
    }
    
    function Footer() {
        $this->SetY(-15);
        $this->SetFont('Arial', 'I', 8);
        $this->Cell(0, 10, 'Page '.$this->PageNo().'/{nb}', 0, 0, 'C');
    }
}

if(isset($_GET['start_date']) && isset($_GET['end_date'])) {
    $start_date = $_GET['start_date'];
    $end_date = $_GET['end_date'];
    
    $pdf = new PDF('P', 'mm', 'Letter');
    $pdf->AliasNbPages();
    $pdf->AddPage();
    
    // Title
    $pdf->SetFont('Arial', 'B', 16);
    $pdf->Cell(0, 10, 'MONTHLY EXPENSES REPORT', 0, 1, 'C');
    
    // Report Period
    $pdf->SetFillColor(240, 240, 240);
    $pdf->Rect(10, $pdf->GetY(), 195, 15, 'F');
    
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(30, 10, 'Period:', 0, 0);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(0, 10, date('F d', strtotime($start_date)).' - '.date('F d, Y', strtotime($end_date)), 0, 1);
    
    // Table header
    $pdf->Ln(10);
    $pdf->SetFillColor(50, 50, 50);
    $pdf->SetTextColor(255, 255, 255);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(40, 8, 'Date', 1, 0, 'C', true);
    $pdf->Cell(95, 8, 'Description', 1, 0, 'C', true);
    $pdf->Cell(60, 8, 'Amount', 1, 1, 'C', true);

    // Reset text color for data
    $pdf->SetTextColor(0, 0, 0);
    $pdf->SetFont('Arial', '', 9);

    // Fetch expense records
    $sql = "SELECT 
                DATE(created_at) as expense_date,
                description,
                amount
            FROM expenses 
            WHERE DATE(created_at) BETWEEN ? AND ?
            ORDER BY DATE(created_at)";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$start_date, $end_date]);
    $expense_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $total_expenses = 0;
    foreach($expense_records as $row) {
        $pdf->Cell(40, 7, date('M d, Y', strtotime($row['expense_date'])), 1, 0, 'C');
        $pdf->Cell(95, 7, $row['description'], 1, 0, 'L');
        $pdf->Cell(60, 7, 'PHP '.number_format($row['amount'], 2), 1, 1, 'R');
        $total_expenses += $row['amount'];
    }

    // Total expenses
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(135, 8, 'Total Expenses:', 1, 0, 'R');
    $pdf->Cell(60, 8, 'PHP '.number_format($total_expenses, 2), 1, 1, 'R');

    // Signature section
    $pdf->Ln(20);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(95, 5, 'Prepared by:', 0, 0);
    $pdf->Cell(95, 5, 'Approved by:', 0, 1);

    $pdf->Ln(15);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(95, 5, '_____________________', 0, 0, 'C');
    $pdf->Cell(95, 5, '_____________________', 0, 1, 'C');
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(95, 5, 'Finance Officer', 0, 0, 'C');
    $pdf->Cell(95, 5, 'KENNETH LEONIDA', 0, 1, 'C');
    $pdf->Cell(95, 5, '', 0, 0, 'C');
    $pdf->Cell(95, 5, 'Owner', 0, 1, 'C');

    // Output PDF
    $filename = isset($_GET['filename']) ? $_GET['filename'] : 'expense_report';
    $pdf->Output($filename.'.pdf', 'I');
    exit();
}
?>
