<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';


// Fetch all employees for the dropdown
$sql = "SELECT * FROM profile";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single row

// Ensure business_name is retrieved correctly
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad"; 
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Laundry Management System</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
  :root {
    --primary-color: #4B89DC;
    --accent-color: #5D9CEC;
    --hover-color: #48CFAD;
    --text-color: #434A54;
    --sidebar-text: #fff;
  }
  
  body {
    font-family: 'Poppins', sans-serif;
    background: #F5F7FA;
  }

  .wrapper {
    display: flex;
  }

  #sidebar {
    min-width: 250px;
    max-width: 250px;
    background: var(--primary-color);
    color: var(--sidebar-text);
    transition: all 0.3s;
    border-radius: 0 25px 25px 0;
  }

  #sidebar.active {
    margin-left: -250px;
  }

  .sidebar-header {
    padding: 25px;
    background: var(--accent-color);
    text-align: center;
    border-radius: 0 25px 0 0;
  }

  .sidebar-header h3 {
    color: var(--sidebar-text);
    font-weight: 600;
    font-size: 1.8em;
    margin: 0;
  }

  #sidebar ul li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
  }

  #sidebar ul li a {
    color: var(--sidebar-text);
    padding: 12px 25px;
    display: block;
    text-decoration: none;
    transition: all 0.2s;
    border-radius: 0 20px 20px 0;
    margin-right: 15px;
  }

  #sidebar ul li a:hover {
    background: var(--hover-color);
    transform: translateX(5px);
  }

  #sidebar ul li.active a {
    background: var(--hover-color);
    color: #fff;
  }

  #sidebar ul li a i {
    margin-right: 10px;
    font-size: 1.1em;
  }

  #content {
    width: 100%;
    padding: 25px;
  }

  .navbar {
    background: white !important;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    border-radius: 15px;
  }

  #sidebarCollapse {
    background: var(--accent-color);
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    transition: all 0.2s;
  }

  #sidebarCollapse:hover {
    background: var(--hover-color);
    transform: scale(1.05);
  }

  .btn-danger {
    background: #FC6E51;
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
  }

  .btn-danger:hover {
    background: #E9573F;
    transform: scale(1.05);
  }

  .container-fluid {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
  }

  /* Add some playful animations */
  #sidebar ul li a i {
    transition: transform 0.3s;
  }

  #sidebar ul li a:hover i {
    transform: rotate(360deg);
  }
</style>

</head>
<!-- Rest of the HTML remains the same -->
<body>
  <div class="wrapper">
    <!-- Sidebar -->
    <nav id="sidebar">
      <div class="sidebar-header">
        <h3 class="text-center mb-4"><?php echo htmlspecialchars($business_name, ENT_QUOTES); ?></h3>
        <div class="user-info">
          <p class="welcome-text">
            Welcome, <?php echo isset($_SESSION['full_name']) ? $_SESSION['full_name'] : 'Guest'; ?>
            <small class="d-block text-light">
              ID: <?php echo isset($_SESSION['employee_id']) ? $_SESSION['employee_id'] : 'N/A'; ?>
            </small>
            <small class="d-block text-light">
              User Type: <?php echo isset($_SESSION['type']) ? $_SESSION['type'] : 'N/A'; ?>
            </small>
          </p>
        </div>
      </div>

      <ul class="list-unstyled components">
        <?php if (isset($_SESSION['type']) && $_SESSION['type'] === 'Administrator'): ?>
          <li class="active">
        <a href="dashboard.php"><i class="fas fa-home"></i> Dashboard</a>
          </li>
          <li>
        <a href="pages/employee.php"><i class="fas fa-users"></i> Employees</a>
          </li>
          <li>
        <a href="pages/customer.php"><i class="fas fa-user-friends"></i> Customers</a>
          </li>
          <li>
        <a href="pages/transactions.php"><i class="fas fa-cash-register"></i> Transactions</a>
          </li>
          <li>
        <a href="pages/expenses.php"><i class="fas fa-money-bill"></i> Expenses</a>
          </li>
          <li>
        <a href="pages/reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
          </li>
          <li>
        <a href="pages/biometric.php"><i class="fas fa-fingerprint"></i> Biometric</a>
          </li>
          <li>
        <a href="pages/user.php"><i class="fas fa-fingerprint"></i> User Account</a>
          </li>
            <li>
          <a href="pages/pricing.php"><i class="fas fa-tags"></i> Price Settings</a>
            </li>
             <li>
            <a href="pages/profile.php"><i class="fas fa-building"></i> Business Profile</a>
            </li>
            <li>
            <a href="reports/payroll_section.php"><i class="fas fa-money-check-alt"></i> Payroll</a>
            </li>
        <?php else: ?>
           <li>
        <a href="pages/customer.php"><i class="fas fa-user-friends"></i> Customers</a>
          </li>
          <li>
        <a href="pages/transactions.php"><i class="fas fa-cash-register"></i> Transactions</a>
          </li>
          <li>
        <a href="pages/biometric.php"><i class="fas fa-fingerprint"></i> Biometric</a>
            </li>
        <?php endif; ?>
      </ul>
    </nav>

    <!-- Page Content -->
    <div id="content">
      <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
          <button type="button" id="sidebarCollapse" class="btn btn-info">
        <i class="fas fa-align-left"></i>
        <span>Toggle Menu</span>
          </button>
          <div class="clock-container" style="font-size: 1.2em; color: var(--primary-color); animation: pulse 2s infinite;">
        <i class="far fa-clock"></i>
        <span id="current-time"></span>
        <span style="margin: 0 10px;">|</span>
        <i class="far fa-calendar-alt"></i>
        <span id="current-date"></span>
          </div>
          <div class="float-end">
        <a href="login.php" class="btn btn-danger">Logout</a>
          </div>
        </div>
      </nav>

      <style>
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
        }
      </style>
      <div class="container-fluid mb-4">
        <h4>Sales and Expenses Report for <?php echo date('F Y'); ?></h4>
      </div>

      <div class="container-fluid">
        <div class="row">
          <div class="col-md-4">
            <div class="card shadow-sm">
              <div class="card-body">
                <h5 class="card-title">Sales</h5>
                <canvas id="salesChart"></canvas>
                <h3 class="text-center mt-3" id="totalSales">Total: ₱0.00</h3>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card shadow-sm">
              <div class="card-body">
                <h5 class="card-title">Expenses</h5>
                <canvas id="expensesChart"></canvas>
                <h3 class="text-center mt-3" id="totalExpenses">Total: ₱0.00</h3>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card shadow-sm">
              <div class="card-body">
                <h5 class="card-title">Monthly Profit</h5>
                <div id="profitDisplay" class="text-center">
                  <canvas id="profitChart"></canvas>
                  <h3 class="text-center mt-3" id="totalProfit">Total: ₱0.00</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      <script>
      let salesChart, expensesChart, profitChart;

      // Function to fetch current month's data
      function fetchCurrentMonthData() {
        try {
          const now = new Date();
          const startDate = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-01`;
          const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
          
          fetch(`includes/get_sales_expenses.php?start=${startDate}&end=${endDate}`)
            .then(response => {
              if (!response.ok) {
                throw new Error('Network response was not ok');
              }
              return response.json();
            })
            .then(data => {
              if (!data || data.error) {
                throw new Error(data.error || 'Invalid data received');
              }
              // Calculate monthly profit
              data.monthlyProfit = (data.totalSales || 0) - (data.totalExpenses || 0);
              updateCharts(data);
            })
            .catch(error => {
              console.error('Error:', error);
              alert('Failed to fetch data: ' + error.message);
              // Set default values when data fetch fails
              updateCharts({
                totalSales: 0,
                totalExpenses: 0,
                monthlyProfit: 0
              });
            });
        } catch (error) {
          console.error('Error in fetchCurrentMonthData:', error);
          alert('An unexpected error occurred while fetching data');
        }
      }

      function updateCharts(data) {
        try {
          // Format numbers with commas and peso sign
          const formatCurrency = num => `₱${(Number(num) || 0).toLocaleString('en-PH', { minimumFractionDigits: 2 })}`;
          
          // Update totals with animation
          animateValue('totalSales', data.totalSales || 0, formatCurrency);
          animateValue('totalExpenses', data.totalExpenses || 0, formatCurrency);
          animateValue('totalProfit', data.monthlyProfit || 0, formatCurrency);
          
          // Common chart options
          const chartOptions = {
            responsive: true,
            animation: {
              duration: 1500,
              easing: 'easeInOutQuart'
            },
            plugins: {
              legend: {
                labels: {
                  font: {
                    family: 'Poppins',
                    size: 12
                  }
                }
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                grid: {
                  display: false
                },
                ticks: {
                  callback: value => formatCurrency(value)
                }
              },
              x: {
                grid: {
                  display: false
                }
              }
            }
          };

          // Safely destroy existing charts
          if (typeof salesChart !== 'undefined' && salesChart) salesChart.destroy();
          if (typeof expensesChart !== 'undefined' && expensesChart) expensesChart.destroy();
          if (typeof profitChart !== 'undefined' && profitChart) profitChart.destroy();

          // Create charts with error handling
          try {
            salesChart = new Chart(document.getElementById('salesChart'), {
              type: 'bar',
              data: {
                labels: ['Current Month Sales'],
                datasets: [{
                  label: 'Sales',
                  data: [data.totalSales || 0],
                  backgroundColor: 'rgba(72, 207, 173, 0.6)',
                  borderColor: 'rgba(72, 207, 173, 1)',
                  borderWidth: 2,
                  borderRadius: 8,
                  barThickness: 50
                }]
              },
              options: chartOptions
            });

            expensesChart = new Chart(document.getElementById('expensesChart'), {
              type: 'bar',
              data: {
                labels: ['Current Month Expenses'],
                datasets: [{
                  label: 'Expenses',
                  data: [data.totalExpenses || 0],
                  backgroundColor: 'rgba(252, 110, 81, 0.6)',
                  borderColor: 'rgba(252, 110, 81, 1)',
                  borderWidth: 2,
                  borderRadius: 8,
                  barThickness: 50
                }]
              },
              options: chartOptions
            });

            profitChart = new Chart(document.getElementById('profitChart'), {
              type: 'bar',
              data: {
                labels: ['Current Month Profit'],
                datasets: [{
                  label: 'Profit',
                  data: [data.monthlyProfit || 0],
                  backgroundColor: 'rgba(75, 137, 220, 0.6)',
                  borderColor: 'rgba(75, 137, 220, 1)',
                  borderWidth: 2,
                  borderRadius: 8,
                  barThickness: 50
                }]
              },
              options: chartOptions
            });

          } catch (chartError) {
            console.error('Error creating charts:', chartError);
            alert('Failed to create charts. Please refresh the page.');
          }
        } catch (error) {
          console.error('Error in updateCharts:', error);
          alert('An error occurred while updating the charts');
        }
      }

      // Animate number counting
      function animateValue(elementId, value, formatter) {
        const duration = 1500;
        const start = 0;
        const end = value;
        const range = end - start;
        const startTime = performance.now();
        
        function update(currentTime) {
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / duration, 1);
          
          const current = Math.floor(progress * range);
          const element = document.getElementById(elementId);
          if (element) {
            element.textContent = `Total: ${formatter(current)}`;
          }
          
          if (progress < 1) {
            requestAnimationFrame(update);
          }
        }
        
        requestAnimationFrame(update);
      }

      // Fetch data when page loads
      document.addEventListener('DOMContentLoaded', fetchCurrentMonthData);
      </script>
      <script>
        function updateDateTime() {
          const now = new Date();
          const timeElement = document.getElementById('current-time');
          const dateElement = document.getElementById('current-date');
          
          timeElement.textContent = now.toLocaleTimeString();
          dateElement.textContent = now.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
          });
        }

        setInterval(updateDateTime, 1000);
        updateDateTime();
      </script>
     
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="assets/js/main.js"></script>
  <script>
    $(document).ready(function() {
      $('#sidebarCollapse').on('click', function() {
        $('#sidebar').toggleClass('active');
      });
    });
  </script>
</body>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="assets/js/main.js"></script>
</body>
</html>

