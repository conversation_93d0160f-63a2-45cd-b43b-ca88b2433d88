# Payment Processing Troubleshooting Guide

## Issue: "Payment processing failed. Please try again."

This error can occur due to several reasons. Follow this step-by-step guide to identify and fix the issue.

## Step 1: Quick Schema Fix (Most Common Issue)

The most common cause is missing database columns. Run the quick fix:

1. **Open your browser and navigate to:**
   ```
   http://your-domain/lmsv2/quick_schema_fix.php
   ```

2. **This script will:**
   - Check if required columns exist in the transactions table
   - Add missing columns if needed
   - Update existing transaction data
   - Add performance indexes

3. **If columns were added, try payment processing again**

## Step 2: Debug Specific Transaction

If the schema fix didn't resolve the issue, debug a specific transaction:

1. **Navigate to a transaction that's failing:**
   ```
   http://your-domain/lmsv2/debug_payment_issue.php?transaction_id=YOUR_TRANSACTION_ID
   ```

2. **The debug script will show:**
   - Database schema status
   - Transaction data
   - Transaction details and totals
   - Payment history
   - Customer information
   - Loyalty points
   - Validation results
   - Specific recommendations

## Step 3: Common Issues and Solutions

### Issue 1: Transaction Already Fully Paid
**Symptoms:** Error message about transaction being fully paid
**Solution:** 
- Check if the transaction balance is already zero
- You cannot add payments to fully paid transactions
- Create a new transaction if needed

### Issue 2: Missing Transaction Items
**Symptoms:** Transaction has no items or zero total
**Solution:**
- Add laundry items to the transaction first
- Go to transaction details and add services before payment

### Issue 3: Database Schema Issues
**Symptoms:** Columns missing errors in debug output
**Solution:**
- Run the `quick_schema_fix.php` script
- Or manually execute the `database/schema_improvements.sql` script

### Issue 4: Validation Errors
**Symptoms:** Specific validation error messages
**Common Solutions:**
- **Invalid payment method:** Use 'cash', 'gcash', 'credit', or 'points'
- **Missing GCash reference:** Required for GCash payments
- **Insufficient points:** Customer doesn't have enough loyalty points
- **Invalid amount:** Payment amount must be positive and reasonable

### Issue 5: Customer Not Found
**Symptoms:** Customer-related errors
**Solution:**
- Ensure the transaction has a valid customer assigned
- Check if customer exists in the database

## Step 4: Manual Database Checks

If automated tools don't help, check manually:

### Check Transaction Structure
```sql
-- Check if transaction exists and has proper structure
SELECT * FROM transactions WHERE transaction_id = YOUR_TRANSACTION_ID;

-- Check transaction details
SELECT * FROM transaction_details WHERE transaction_id = YOUR_TRANSACTION_ID;

-- Check existing payments
SELECT * FROM payments WHERE transaction_id = YOUR_TRANSACTION_ID;
```

### Check Required Columns
```sql
-- Check if new columns exist
SHOW COLUMNS FROM transactions LIKE 'total_amount';
SHOW COLUMNS FROM transactions LIKE 'amount_paid';
SHOW COLUMNS FROM transactions LIKE 'balance';
SHOW COLUMNS FROM transactions LIKE 'status';
```

### Check Customer and Loyalty Points
```sql
-- Check customer
SELECT c.* FROM customers c 
INNER JOIN transactions t ON c.customer_id = t.customer_id 
WHERE t.transaction_id = YOUR_TRANSACTION_ID;

-- Check loyalty points
SELECT * FROM loyalty_points 
WHERE customer_id = YOUR_CUSTOMER_ID AND status = 'AVAILABLE';
```

## Step 5: Enable Error Logging

To get more detailed error information:

### 1. Enable PHP Error Logging
Add to your `config/database.php` or create a `.htaccess` file:
```php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
```

### 2. Check Server Error Logs
- Look in your server's error log file
- Common locations: `/var/log/apache2/error.log` or similar
- In Laragon: Check the logs folder

### 3. Add Custom Logging
Add this to your payment processing code:
```php
error_log("Payment processing attempt: " . print_r($_POST, true));
error_log("Customer ID: " . $customer_info['customer_id']);
error_log("Transaction balance: " . $current_balance);
```

## Step 6: Test Different Payment Methods

Try different payment methods to isolate the issue:

### Test Cash Payment
- Amount: Any positive number
- Method: 'cash'
- No additional fields required

### Test GCash Payment
- Amount: Any positive number  
- Method: 'gcash'
- GCash Reference: Any string (5+ characters)

### Test Points Payment
- Method: 'points'
- Points Used: Number of points to use
- Ensure customer has available points

## Step 7: Browser Developer Tools

Check browser console for JavaScript errors:

1. **Open Developer Tools** (F12)
2. **Go to Console tab**
3. **Try payment processing**
4. **Look for any JavaScript errors**
5. **Check Network tab for failed requests**

## Step 8: Verify Form Data

Ensure the payment form is sending correct data:

### Required Form Fields
```html
<input name="payment_method" value="cash|gcash|credit|points">
<input name="amount_paid" value="positive_number">
<input name="gcash_reference" value="reference_if_gcash">
<input name="points_used" value="points_if_points_payment">
```

### Check Form Submission
Add temporary debugging in `transaction_details.php`:
```php
if (isset($_POST['process_payment'])) {
    echo "<pre>POST data: ";
    print_r($_POST);
    echo "</pre>";
    exit; // Remove after debugging
}
```

## Step 9: Database Connection Issues

If you suspect database connection problems:

### Test Database Connection
```php
try {
    $test = $conn->query("SELECT 1");
    echo "Database connection OK";
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
}
```

### Check Database Permissions
- Ensure database user has INSERT, UPDATE, SELECT permissions
- Check if user can access all required tables

## Step 10: Contact Support

If none of the above steps resolve the issue:

### Gather Information
1. **Error messages** from debug script
2. **Server error logs** 
3. **Browser console errors**
4. **Database schema status**
5. **PHP version and configuration**

### Provide Details
- Exact steps to reproduce the issue
- Transaction ID that's failing
- Payment method being used
- Any recent changes to the system

## Prevention Tips

### Regular Maintenance
1. **Backup database** before making changes
2. **Test payment processing** after updates
3. **Monitor error logs** regularly
4. **Keep system updated**

### Development Best Practices
1. **Use the debug tools** when developing
2. **Test all payment methods** thoroughly
3. **Validate form data** properly
4. **Handle errors gracefully**

## Quick Reference Commands

### Run Schema Fix
```
http://your-domain/lmsv2/quick_schema_fix.php
```

### Debug Transaction
```
http://your-domain/lmsv2/debug_payment_issue.php?transaction_id=ID
```

### Check Database Schema
```sql
DESCRIBE transactions;
```

### View Recent Errors
```sql
SELECT * FROM payments ORDER BY created_at DESC LIMIT 10;
```

This troubleshooting guide should help you identify and resolve most payment processing issues. Start with Step 1 (Quick Schema Fix) as it resolves the most common problems.
