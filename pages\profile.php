<?php
session_start();
include_once '../config/database.php';

// Process form submission for creating profile
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create') {
    try {
        $sql = "INSERT INTO profile (business_name, business_owner, business_address, business_cell, business_land) 
                VALUES (:business_name, :business_owner, :business_address, :business_cell, :business_land)";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':business_name' => trim($_POST['business_name']),
            ':business_owner' => trim($_POST['business_owner']),
            ':business_address' => trim($_POST['business_address']),
            ':business_cell' => trim($_POST['business_cell']),
            ':business_land' => trim($_POST['business_land'])
        ]);
        
        echo json_encode(['success' => true]);
        exit;
    } catch(PDOException $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }
}
// Add update handler
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update') {
    try {
        // Validate inputs
        if (empty($_POST['business_name']) || empty($_POST['business_owner']) || 
            empty($_POST['business_address']) || empty($_POST['business_cell']) || 
            !isset($_POST['profile_id'])) {
            throw new Exception('Required fields are missing');
        }

        $sql = "UPDATE profile SET business_name=?, business_owner=?, business_address=?, 
                business_cell=?, business_land=? WHERE profileid=?";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            trim($_POST['business_name']),
            trim($_POST['business_owner']),
            trim($_POST['business_address']),
            trim($_POST['business_cell']),
            trim($_POST['business_land']),
            $_POST['profile_id']
        ]);
        
        if ($stmt->rowCount() === 0) {
            throw new Exception('No profile found with the given ID');
        }
        
        echo json_encode(['success' => true]);
        exit;
    } catch(Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }
}
// Check if profile exists
$checkProfile = $conn->query("SELECT * FROM profile LIMIT 1");
$profile = $checkProfile->fetch(PDO::FETCH_ASSOC);
$profile_id = $profile['profileid'] ?? null;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Profile</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .profile-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 20px;
            transform: translateY(0);
            transition: all 0.3s ease;
        }
        
        .profile-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(13,110,253,0.15);
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.8rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13,110,253,0.1);
            transform: scale(1.01);
        }

        .profile-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: none;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: inset 0 0 20px rgba(0,0,0,0.03);
        }

        .profile-title {
            background: linear-gradient(45deg, #0d6efd, #0099ff);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 2.5rem;
            text-align: center;
            font-weight: 700;
            font-size: 2.2rem;
        }

        .profile-label {
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .btn-primary {
            padding: 0.8rem 2.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #0d6efd, #0099ff);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(13,110,253,0.3);
            background: linear-gradient(45deg, #0099ff, #0d6efd);
        }
    </style>
</head>
<body>

<?php if ($profile): ?>
    <div class="profile-container">
        <h2 class="profile-title">
            <svg role="img" aria-label="Edit Business Profile" class="icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <title>Edit Business Profile</title>
                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
            </svg>
            Edit Business Profile
        </h2>
        <div class="profile-card">
            <form id="updateProfileForm" class="needs-validation" novalidate>
                <input type="hidden" name="profile_id" value="<?php echo htmlspecialchars($profile_id); ?>">
                <div class="mb-4">
                    <label for="business_name" class="form-label profile-label">
                        <i class="fas fa-building text-primary"></i> Business Name
                    </label>
                    <input type="text" class="form-control" id="business_name" name="business_name" 
                        value="<?php echo htmlspecialchars($profile['business_name'] ?? ''); ?>" required>
                    <div class="invalid-feedback">Please enter business name</div>
                </div>

                <div class="mb-4">
                    <label for="business_owner" class="form-label profile-label">
                        <i class="fas fa-user-tie text-secondary"></i> Business Owner
                    </label>
                    <input type="text" class="form-control" id="business_owner" name="business_owner" 
                        value="<?php echo htmlspecialchars($profile['business_owner'] ?? ''); ?>" required>
                    <div class="invalid-feedback">Please enter business owner name</div>
                </div>

                <div class="mb-4">
                    <label for="business_address" class="form-label profile-label">
                        <i class="fas fa-map-marker-alt text-danger"></i> Business Address
                    </label>
                    <input type="text" class="form-control" id="business_address" name="business_address" 
                        value="<?php echo htmlspecialchars($profile['business_address']); ?>" required>
                    <div class="invalid-feedback">Please enter business address</div>
                </div>

                <div class="mb-4">
                    <label for="business_cell" class="form-label profile-label">
                        <i class="fas fa-mobile-alt text-success"></i> Cell Phone
                    </label>
                    <input type="tel" class="form-control" id="business_cell" name="business_cell" 
                        value="<?php echo htmlspecialchars($profile['business_cell']); ?>" required pattern="[0-9]{10,}">
                    <div class="invalid-feedback">Please enter a valid phone number</div>
                </div>

                <div class="mb-4">
                    <label for="business_land" class="form-label profile-label">
                        <i class="fas fa-phone text-warning"></i> Landline
                    </label>
                    <input type="tel" class="form-control" id="business_land" name="business_land" 
                        value="<?php echo htmlspecialchars($profile['business_land']); ?>">
                </div>

                <div class="text-center d-flex justify-content-center gap-3">
                    <button type="submit" class="btn btn-primary px-4">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                    <a href="../index.php" class="btn btn-secondary">
                        <i class="fas fa-home"></i> Back to Homepage
                    </a>
                </div>

            </form>
        </div>
    </div>
<?php else: ?>
    <div class="profile-container">
        <h2 class="profile-title">
            <svg role="img" aria-label="Create Business Profile" class="icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <title>Create Business Profile</title>
                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
            </svg>
            Create Business Profile
        </h2>
        <div class="profile-card">
            <form id="createProfileForm" class="needs-validation" novalidate>
                <div class="mb-4">
                    <label for="business_name" class="form-label profile-label">Business Name</label>
                    <input type="text" class="form-control" id="business_name" name="business_name" required>
                    <div class="invalid-feedback">Please enter business name</div>
                </div>
                <div class="mb-4">
                    <label for="business_owner" class="form-label profile-label">Business Owner</label>
                    <input type="text" class="form-control" id="business_owner" name="business_owner" required>
                    <div class="invalid-feedback">Please enter business owner name</div>
                </div>
                <div class="mb-4">
                    <label for="business_address" class="form-label profile-label">Business Address</label>
                    <input type="text" class="form-control" id="business_address" name="business_address" required>
                    <div class="invalid-feedback">Please enter business address</div>
                </div>
                <div class="mb-4">
                    <label for="business_cell" class="form-label profile-label">Cell Phone</label>
                    <input type="tel" class="form-control" id="business_cell" name="business_cell" required pattern="[0-9]{10,}">
                    <div class="invalid-feedback">Please enter a valid phone number</div>
                </div>
                <div class="mb-4">
                    <label for="business_land" class="form-label profile-label">Landline</label>
                    <input type="tel" class="form-control" id="business_land" name="business_land">
                </div>
                <div class="text-center">
                    <button type="submit" class="btn btn-primary px-4">Create Profile</button>
                </div>
            </form>
        </div>
    </div>
<?php endif; ?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
$(document).ready(function() {
    const createForm = $('#createProfileForm');
    const updateForm = $('#updateProfileForm');
    
    createForm.on('submit', function(e) {
        e.preventDefault();
        
        if (!this.checkValidity()) {
            e.stopPropagation();
            $(this).addClass('was-validated');
            return;
        }

        $.ajax({
            url: 'profile.php',
            type: 'POST',
            data: $(this).serialize() + '&action=create',
            beforeSend: function() {
                $('button[type="submit"]').prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span> Creating...');
            },
            success: function(response) {
                try {
                    const result = JSON.parse(response);
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + result.error);
                    }
                } catch(e) {
                    alert('Error creating profile!');
                }
            },
            error: function() {
                alert('Error creating profile!');
            },
            complete: function() {
                $('button[type="submit"]').prop('disabled', false).text('Create Profile');
            }
        });
    });

    updateForm.on('submit', function(e) {
        e.preventDefault();
        
        if (!this.checkValidity()) {
            e.stopPropagation();
            $(this).addClass('was-validated');
            return;
        }

        $.ajax({
            url: 'profile.php',
            type: 'POST',
            data: $(this).serialize() + '&action=update',
            beforeSend: function() {
                $('button[type="submit"]').prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span> Saving...');
            },
            success: function(response) {
                try {
                    const result = JSON.parse(response);
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + result.error);
                    }
                } catch(e) {
                    alert('Error updating profile!');
                }
            },
            error: function() {
                alert('Error updating profile!');
            },
            complete: function() {
                $('button[type="submit"]').prop('disabled', false).text('Save Changes');
            }
        });
    });
});
</script>

</body>
</html>
