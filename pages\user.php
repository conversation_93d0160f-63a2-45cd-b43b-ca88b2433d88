<?php
session_start();
include_once '../config/database.php'; 

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $employee_id = $_POST['employee_id'];
        $username = $_POST['username'];
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $type = $_POST['type'];
        $user_status = $_POST['user_status'];
        
        $sql = "INSERT INTO users (username, password, employee_id, type, user_status) VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$username, $password, $employee_id, $type, $user_status]);
    } elseif (isset($_POST['update'])) {
        $id = $_POST['id'];
        $type = $_POST['type'];
        $user_status = $_POST['user_status'];
        
        $sql = "UPDATE users SET type = ?, user_status = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$type, $user_status, $id]);
    }
}

// Fetch all employees for the dropdown
$sql = "SELECT employee_id, full_name FROM employees";
$stmt = $conn->prepare($sql);
$stmt->execute();
$employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Search functionality for users
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$sql = "SELECT u.*, e.full_name 
        FROM users u 
        JOIN employees e ON u.employee_id = e.employee_id 
        WHERE e.full_name LIKE ? OR u.username LIKE ?";
$search_term = "%$search%";
$stmt = $conn->prepare($sql);
$stmt->execute([$search_term, $search_term]);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);

$id = $_GET['id'] ?? 0;
$sql = "SELECT u.*, e.full_name FROM users u JOIN employees e ON u.employee_id = e.employee_id WHERE u.id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$id]);
$user = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>User Account Management</title>
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; }
        .table thead th { background-color: #0d6efd; color: white; }
        .input-group { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="d-flex align-items-center justify-content-between mb-3">
            <h2>User Account Management</h2>
            <?php
            if (isset($_SESSION['message'])) {
                echo '<div class="alert alert-info mb-0">' . $_SESSION['message'] . '</div>';
                unset($_SESSION['message']);
            }
            ?>
        </div>
        
        <!-- Search Form -->
        <form method="GET" class="mb-4">
            <div class="input-group">
                <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                <input type="text" name="search" class="form-control" placeholder="Search users..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-primary">Search</button>
            </div>
        </form>

        <!-- Add User Form -->
        <form method="POST" class="mb-4">
            <div class="row mb-3">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-user"></i></span>
                        <select name="employee_id" class="form-control" required>
                            <option value="">Select Employee</option>
                            <?php foreach ($employees as $employee): ?>
                                <option value="<?php echo htmlspecialchars($employee['employee_id']); ?>">
                                    <?php echo htmlspecialchars($employee['full_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-user"></i></span>
                        <input type="text" name="username" class="form-control" placeholder="Username" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-key"></i></span>
                        <input type="password" name="password" class="form-control" placeholder="Password" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-users"></i></span>
                        <select name="type" class="form-control" required>
                            <option value="">Select Type</option>
                            <option value="User">User</option>
                            <option value="Administrator">Administrator</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-toggle-on"></i></span>
                        <select name="user_status" class="form-control" required>
                            <option value="">Select Status</option>
                            <option value="Active">Active</option>
                            <option value="Inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <button type="submit" name="save" class="btn btn-success">Create User</button>
                    <a href="../index.php" class="btn btn-secondary">Home</a>
                </div>
            </div>
        </form>

        <!-- Users Table -->
        <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-striped">
            <thead style="position: sticky; top: 0; background: white;">
                <tr>
                <th><i class="fa-solid fa-id-card"></i> ID</th>
                <th><i class="fa-solid fa-user"></i> Employee Name</th>
                <th><i class="fa-solid fa-user-tag"></i> Username</th>
                <th><i class="fa-solid fa-users"></i> Type</th>
                <th><i class="fa-solid fa-toggle-on"></i> Status</th>
                <th><i class="fa-solid fa-key"></i> Password</th>
                <th><i class="fa-solid fa-cog"></i> Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($result as $row): ?>
                <tr>
                <td><?php echo htmlspecialchars($row['id']); ?></td>
                <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                <td><?php echo htmlspecialchars($row['username']); ?></td>
                <td>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="id" value="<?php echo htmlspecialchars($row['id']); ?>">
                        <select name="type" class="form-control form-control-sm" onchange="this.form.submit()">
                            <option value="User" <?php echo $row['type'] == 'User' ? 'selected' : ''; ?>>User</option>
                            <option value="Administrator" <?php echo $row['type'] == 'Administrator' ? 'selected' : ''; ?>>Administrator</option>
                        </select>
                        <input type="hidden" name="update" value="1">
                        <input type="hidden" name="user_status" value="<?php echo htmlspecialchars($row['user_status']); ?>">
                    </form>
                </td>
                <td>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="id" value="<?php echo htmlspecialchars($row['id']); ?>">
                        <select name="user_status" class="form-control form-control-sm" onchange="this.form.submit()">
                            <option value="Active" <?php echo $row['user_status'] == 'Active' ? 'selected' : ''; ?>>Active</option>
                            <option value="Inactive" <?php echo $row['user_status'] == 'Inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                        <input type="hidden" name="update" value="1">
                        <input type="hidden" name="type" value="<?php echo htmlspecialchars($row['type']); ?>">
                    </form>
                </td>
                <td>****************************</td>
                <td>
                    <form method="POST" action="../includes/edit_user.php" style="display: inline;">
                    <input type="hidden" name="id" value="<?php echo htmlspecialchars($row['id']); ?>">
                    <button type="submit" name="reset_password" class="btn btn-sm btn-warning" 
                        onclick="return confirm('Are you sure you want to reset this user\'s password to default?');">
                        <i class="fa-solid fa-key"></i> Reset Password
                    </button>
                    </form>
                </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
