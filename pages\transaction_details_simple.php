<?php
include_once '../config/database.php';
session_start();

// Initialize variables
$transaction_id = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';
$customer_info = [];
$total_amount = 0;
$addons_total = 0;
$balance = 0;

// Redirect if no transaction ID provided
if (empty($transaction_id)) {
    $_SESSION['error_message'] = "Transaction ID is required.";
    header("Location: transactions.php");
    exit();
}

// Get transaction and customer details
if ($transaction_id) {
    $sql = "SELECT t.*, c.full_name, c.phone_number, c.address,
                   SUM(CASE WHEN lp.status = 'AVAILABLE' THEN lp.points ELSE 0 END) as available_points
            FROM transactions t
            INNER JOIN customers c ON t.customer_id = c.customer_id 
            LEFT JOIN loyalty_points lp ON c.customer_id = lp.customer_id
            WHERE t.transaction_id = ?
            GROUP BY t.transaction_id";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$transaction_id]);
    $customer_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$customer_info) {
        $_SESSION['error_message'] = "Transaction not found.";
        header("Location: transactions.php");
        exit();
    }
    
    // Get total amount paid for this transaction
    $payment_sql = "SELECT SUM(amount_paid) as TotalPayment FROM payments WHERE transaction_id = ?";
    $stmt = $conn->prepare($payment_sql);
    $stmt->execute([$transaction_id]);
    $total_payment = $stmt->fetch(PDO::FETCH_ASSOC);
    $TotalPayment = $total_payment['TotalPayment'] ?? 0;
}

// Get existing transaction details
$details_sql = "SELECT td.*, td.category as category_name
                FROM transaction_details td
                WHERE td.transaction_id = ?
                ORDER BY td.detail_id";
$stmt = $conn->prepare($details_sql);
$stmt->execute([$transaction_id]);
$transaction_details = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get addon products
$addons_sql = "SELECT a.*, p.name, p.price, p.stock_quantity
               FROM addons a
               JOIN products p ON a.product_id = p.product_id
               WHERE a.transaction_id = ?";
$stmt = $conn->prepare($addons_sql);
$stmt->execute([$transaction_id]);
$addon_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate total amounts
foreach ($transaction_details as $detail) {
    $total_amount += $detail['subtotal'];
}
foreach ($addon_products as $addon) {
    $addons_total += $addon['trans_subtotal'];
}

$grand_total = $total_amount + $addons_total;
$amount_paid = $TotalPayment;
$balance = $grand_total - $amount_paid;

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $transaction_id = $_GET['transaction_id'] ?? null;
    $customer_id = $customer_info['customer_id'] ?? null;

    if (isset($_POST['save_details'])) {
        $category = $_POST['category'];
        $weight = floatval($_POST['weight']);
        $item_remarks = $_POST['item_remarks'] ?? '';

        // Get pricing details from princing_detail table
        $pricing_sql = "SELECT pd.price, pd.kilogram, pd.num_load
                        FROM pricing p
                        JOIN princing_detail pd ON p.id = pd.cat_id
                        WHERE p.id = ? AND pd.kilogram <= ?
                        ORDER BY pd.kilogram DESC
                        LIMIT 1";
        $stmt = $conn->prepare($pricing_sql);
        $stmt->execute([$category, $weight]);
        $pricing = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$pricing) {
            $_SESSION['error_message'] = "No pricing found for selected category and weight.";
            header("Location: transaction_details_simple.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }

        $base_price = $pricing['price'];
        $price_per_kg = $pricing['price'];
        
        // Calculate subtotal based on pricing structure
        if ($pricing['num_load'] > 0) {
            // Load-based pricing
            $loads = ceil($weight / $pricing['kilogram']);
            $subtotal = $base_price * $loads;
        } else {
            // Weight-based pricing
            $subtotal = $price_per_kg * $weight;
        }

        // Get category name for insertion
        $cat_sql = "SELECT category FROM pricing WHERE id = ?";
        $cat_stmt = $conn->prepare($cat_sql);
        $cat_stmt->execute([$category]);
        $category_name = $cat_stmt->fetch(PDO::FETCH_ASSOC)['category'] ?? 'Unknown';

        $sql = "INSERT INTO transaction_details 
                (transaction_id, category, weight, price_per_kg, subtotal, item_remarks, base_price)
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$transaction_id, $category_name, $weight, $price_per_kg, $subtotal, $item_remarks, $base_price]);

        $_SESSION['success_message'] = "Item added successfully!";
        header("Location: transaction_details_simple.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }

    if (isset($_POST['add_product'])) {
        $product_id = $_POST['product_id'];
        $quantity = floatval($_POST['quantity']);

        $product_sql = "SELECT price, stock_quantity FROM products WHERE product_id = ?";
        $stmt = $conn->prepare($product_sql);
        $stmt->execute([$product_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$product) {
            $_SESSION['error_message'] = "Product not found.";
            header("Location: transaction_details_simple.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }

        if ($product['stock_quantity'] >= $quantity) {
            $subtotal = $quantity * $product['price'];

            $sql = "INSERT INTO addons (transaction_id, product_id, trans_qty, trans_subtotal)
                    VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$transaction_id, $product_id, $quantity, $subtotal]);

            $update_stock = "UPDATE products SET stock_quantity = stock_quantity - ? WHERE product_id = ?";
            $stmt = $conn->prepare($update_stock);
            $stmt->execute([$quantity, $product_id]);

            $_SESSION['success_message'] = "Add-on product added successfully!";
        } else {
            $_SESSION['error_message'] = "Insufficient stock quantity!";
        }

        header("Location: transaction_details_simple.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }
}

// Process payments
if (isset($_POST['process_payment'])) {
    $payment_method = $_POST['payment_method'];
    $amount_paid = floatval($_POST['amount_paid'] ?? 0);
    $gcash_reference = $_POST['gcash_reference'] ?? null;
    $points_used = floatval($_POST['points_used'] ?? 0);
    $transaction_amount = $grand_total;
    
    // Validate payment method specific requirements
    if ($payment_method === 'gcash' && empty($gcash_reference)) {
        $_SESSION['error_message'] = "GCash reference number is required for GCash payments.";
        header("Location: transaction_details_simple.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }
    
    if ($payment_method === 'points') {
        // Check if customer has at least 10 points
        if ($customer_info['available_points'] < 10) {
            $_SESSION['error_message'] = "Insufficient points. You need at least 10 points for a free transaction.";
            header("Location: transaction_details_simple.php?transaction_id=" . urlencode($transaction_id));
            exit();
        }
        
        // For points payment, 10 points = 1 free transaction (full payment)
        $points_used = 10;
        $amount_paid = $transaction_amount; // Full payment with points
    }
    
    // Validate payment amount for non-points payments
    if ($payment_method !== 'points' && $amount_paid <= 0) {
        $_SESSION['error_message'] = "Payment amount must be greater than 0.";
        header("Location: transaction_details_simple.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }
    
    // Determine actual payment amount
    $payment_amount = min($amount_paid, $balance);

    try {
        $conn->beginTransaction();

        // Insert payment record
        $payment_sql = "INSERT INTO payments (
            transaction_id, method, transaction_amount, 
            amount_paid, gcash_reference_number, points_used
        ) VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($payment_sql);
        $stmt->execute([
            $transaction_id,
            $payment_method,
            $transaction_amount,
            $payment_amount,
            $gcash_reference,
            $points_used
        ]);

        // Update loyalty points if points were used
        if ($points_used > 0) {
            // Mark exactly 10 points as USED
            $update_points_sql = "UPDATE loyalty_points 
                                SET status = 'USED',
                                    updated_at = CURRENT_TIMESTAMP 
                                WHERE customer_id = ? 
                                AND status = 'AVAILABLE'
                                ORDER BY loyalty_id ASC
                                LIMIT 10";
            $stmt = $conn->prepare($update_points_sql);
            $stmt->execute([$customer_info['customer_id']]);
        }

        // Calculate new balance after payment
        $new_balance = $balance - $payment_amount;
        
        // Update transaction remarks based on balance
        if ($new_balance <= 0) {
            $remarks = 'Completed';
        } elseif ($payment_amount > 0) {
            $remarks = 'Has Balance';
        } else {
            $remarks = 'In Process';
        }
        
        $update_transaction = "UPDATE transactions SET remarks = ? WHERE transaction_id = ?";
        $stmt = $conn->prepare($update_transaction);
        $stmt->execute([$remarks, $transaction_id]);

        $conn->commit();
        $_SESSION['success_message'] = "Payment processed successfully!";
        header("Location: transaction_details_simple.php?transaction_id=" . urlencode($transaction_id));
        exit();

    } catch (Exception $e) {
        $conn->rollBack();
        $_SESSION['error_message'] = "Error processing payment: " . $e->getMessage();
        header("Location: transaction_details_simple.php?transaction_id=" . urlencode($transaction_id));
        exit();
    }
}

// Get available products for add-ons
$products_sql = "SELECT * FROM products WHERE stock_quantity > 0";
$stmt = $conn->prepare($products_sql);
$stmt->execute();
$available_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get pricing categories
$categories_sql = "SELECT * FROM pricing";
$stmt = $conn->prepare($categories_sql);
$stmt->execute();
$pricing_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fresh & Clean Laundry - Transaction Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4A90E2;
            --secondary-color: #67B26F;
            --accent-color: #F7F9FC;
        }
        body {
            font-family: 'Nunito', sans-serif;
            background: linear-gradient(135deg, var(--accent-color) 0%, #ffffff 100%);
            min-height: 100vh;
        }
        .container {
            padding: 1.5rem;
        }
        .customer-info {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 15px;
            padding: 1.2rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .points-badge {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        .card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            background: white;
            margin-bottom: 1rem;
        }
        .card-header {
            background: var(--accent-color);
            border-bottom: 2px solid #eee;
            border-radius: 12px 12px 0 0 !important;
        }
        .btn-primary {
            background: var(--primary-color);
            border: none;
            border-radius: 8px;
        }
        .btn-success {
            background: var(--secondary-color);
            border: none;
            border-radius: 8px;
        }
        .table {
            font-size: 0.9rem;
        }
        .table th {
            font-weight: 600;
            color: var(--primary-color);
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #eee;
        }
        .total-section {
            background: var(--accent-color);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <div class="customer-info">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4><i class="fas fa-user-circle me-2"></i><?php echo htmlspecialchars($customer_info['full_name'] ?? 'Customer'); ?></h4>
                    <p class="mb-0"><i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($customer_info['phone_number'] ?? 'N/A'); ?></p>
                    <p class="mb-0"><i class="fas fa-receipt me-2"></i>Transaction #<?php echo $transaction_id; ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="points-badge">
                        <i class="fas fa-star"></i>
                        <span>Points: <?php echo number_format($customer_info['available_points'] ?? 0); ?></span>
                    </div>
                    <?php if (($customer_info['available_points'] ?? 0) >= 10): ?>
                        <div class="mt-2">
                            <small class="text-light"><i class="fas fa-gift me-1"></i>You can get 1 free transaction!</small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-8">
                <!-- Transaction Items -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Transaction Items</h5>
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addItemModal">
                            <i class="fas fa-plus me-2"></i>Add Item
                        </button>
                    </div>
                    <div class="card-body">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Weight (kg)</th>
                                    <th>Price/kg</th>
                                    <th>Subtotal</th>
                                    <th>Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transaction_details as $detail): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($detail['category_name']); ?></td>
                                    <td><?php echo number_format($detail['weight'], 2); ?></td>
                                    <td>₱<?php echo number_format($detail['price_per_kg'], 2); ?></td>
                                    <td>₱<?php echo number_format($detail['subtotal'], 2); ?></td>
                                    <td><?php echo htmlspecialchars($detail['item_remarks']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <tr class="table-light">
                                    <td colspan="3" class="fw-bold text-primary">Items Total:</td>
                                    <td class="fw-bold text-primary">₱<?php echo number_format($total_amount, 2); ?></td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Add-on Products -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Add-on Products</h5>
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="fas fa-plus me-2"></i>Add Product
                        </button>
                    </div>
                    <div class="card-body">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($addon_products as $addon): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($addon['name']); ?></td>
                                    <td><?php echo number_format($addon['trans_qty']); ?></td>
                                    <td>₱<?php echo number_format($addon['price'], 2); ?></td>
                                    <td>₱<?php echo number_format($addon['trans_subtotal'], 2); ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <tr class="table-light">
                                    <td colspan="3" class="fw-bold text-primary">Add-ons Total:</td>
                                    <td class="fw-bold text-primary">₱<?php echo number_format($addons_total, 2); ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Payment Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="total-section mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Items Total:</span>
                                <strong>₱<?php echo number_format($total_amount, 2); ?></strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Add-ons Total:</span>
                                <strong>₱<?php echo number_format($addons_total, 2); ?></strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Grand Total:</span>
                                <strong>₱<?php echo number_format($grand_total, 2); ?></strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Amount Paid:</span>
                                <strong class="text-success">₱<?php echo number_format($amount_paid, 2); ?></strong>
                            </div>
                            <?php if ($balance > 0): ?>
                            <div class="d-flex justify-content-between text-danger">
                                <span>Remaining Balance:</span>
                                <strong>₱<?php echo number_format($balance, 2); ?></strong>
                            </div>
                            <?php elseif ($balance < 0): ?>
                            <div class="d-flex justify-content-between text-info">
                                <span>Overpayment:</span>
                                <strong>₱<?php echo number_format(abs($balance), 2); ?></strong>
                            </div>
                            <?php else: ?>
                            <div class="d-flex justify-content-between text-success">
                                <span>Status:</span>
                                <strong>FULLY PAID</strong>
                            </div>
                            <?php endif; ?>
                        </div>

                        <?php if ($balance > 0): ?>
                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>" id="paymentForm">
                            <div class="mb-3">
                                <label class="form-label">Payment Method</label>
                                <select name="payment_method" id="paymentMethod" class="form-select" required>
                                    <option value="cash">Cash</option>
                                    <option value="gcash">GCash</option>
                                    <option value="credit">Credit</option>
                                    <?php if (($customer_info['available_points'] ?? 0) >= 10): ?>
                                        <option value="points">Points (10 points = 1 free transaction)</option>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="mb-3 amount-paid-group">
                                <label class="form-label">Amount Tendered</label>
                                <input type="number" step="0.01" name="amount_paid" id="amountTendered" class="form-control" required min="0">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Change</label>
                                <input type="text" id="changeAmount" class="form-control" readonly>
                            </div>

                            <div class="mb-3 gcash-group" style="display: none;">
                                <label class="form-label">GCash Reference Number</label>
                                <input type="text" name="gcash_reference" class="form-control">
                            </div>

                            <div class="mb-3 points-group" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-gift me-2"></i>
                                    Using 10 points will pay for the entire transaction!
                                </div>
                                <input type="hidden" name="points_used" value="10">
                            </div>

                            <button type="submit" name="process_payment" class="btn btn-success w-100">
                                <i class="fas fa-check-circle me-2"></i>Process Payment
                            </button>
                        </form>
                        <?php else: ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            This transaction is fully paid!
                        </div>
                        <?php endif; ?>

                        <a href="transactions.php?customer_id=<?php echo $customer_info['customer_id']; ?>" class="btn btn-secondary w-100 mt-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Transactions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Laundry Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Category</label>
                            <select name="category" class="form-select" required>
                                <?php foreach ($pricing_categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['category']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Weight (kg)</label>
                            <input type="number" step="0.1" name="weight" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Remarks</label>
                            <textarea name="item_remarks" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" name="save_details" class="btn btn-primary">Save Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Product</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Product</label>
                            <select name="product_id" class="form-select" required>
                                <?php foreach ($available_products as $product): ?>
                                <option value="<?php echo $product['product_id']; ?>">
                                    <?php echo htmlspecialchars($product['name']); ?> -
                                    ₱<?php echo number_format($product['price'], 2); ?>
                                    (Stock: <?php echo $product['stock_quantity']; ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Quantity</label>
                            <input type="number" name="quantity" class="form-control" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" name="add_product" class="btn btn-primary">Add Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function calculateChange() {
            const amountTendered = parseFloat(document.getElementById('amountTendered').value) || 0;
            const remainingBalance = <?php echo $balance; ?>;
            const change = amountTendered - remainingBalance;

            if (change >= 0) {
                document.getElementById('changeAmount').value = '₱' + change.toFixed(2);
            } else {
                document.getElementById('changeAmount').value = '₱0.00';
            }
        }

        // Handle payment method changes
        document.addEventListener('DOMContentLoaded', function() {
            const paymentMethodSelect = document.getElementById('paymentMethod');
            if (paymentMethodSelect) {
                paymentMethodSelect.addEventListener('change', function() {
                    const method = this.value;
                    const amountPaidGroup = document.querySelector('.amount-paid-group');
                    const gcashGroup = document.querySelector('.gcash-group');
                    const pointsGroup = document.querySelector('.points-group');

                    // Show/hide fields based on payment method
                    if (method === 'points') {
                        if (amountPaidGroup) amountPaidGroup.style.display = 'none';
                        if (pointsGroup) pointsGroup.style.display = 'block';
                        const amountInput = document.getElementById('amountTendered');
                        if (amountInput) amountInput.required = false;
                    } else {
                        if (amountPaidGroup) amountPaidGroup.style.display = 'block';
                        if (pointsGroup) pointsGroup.style.display = 'none';
                        const amountInput = document.getElementById('amountTendered');
                        if (amountInput) amountInput.required = true;
                    }

                    if (method === 'gcash') {
                        if (gcashGroup) gcashGroup.style.display = 'block';
                    } else {
                        if (gcashGroup) gcashGroup.style.display = 'none';
                    }
                });

                // Add change calculation on input
                const amountInput = document.getElementById('amountTendered');
                if (amountInput) {
                    amountInput.addEventListener('input', calculateChange);
                }
            }
        });
    </script>
</body>
</html>
