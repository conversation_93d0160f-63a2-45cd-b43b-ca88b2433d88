-- Laundry Management System - Database Schema Improvements
-- This script addresses naming inconsistencies, adds missing foreign keys, and optimizes indexes

-- =====================================================
-- 1. FIX NAMING INCONSISTENCIES
-- =====================================================

-- Fix the typo in pricing_detail table name (princing_detail -> pricing_detail)
-- Note: This assumes the table is actually named 'princing_detail' as mentioned
-- If it's already 'pricing_detail', skip this step

-- First, check if the table exists with the typo
-- RENAME TABLE princing_detail TO pricing_detail;

-- =====================================================
-- 2. ADD MISSING FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Add foreign key for addons.product_id -> products.product_id
ALTER TABLE addons 
ADD CONSTRAINT fk_addons_product 
FOREIGN KEY (product_id) REFERENCES products(product_id) 
ON UPDATE CASCADE ON DELETE SET NULL;

-- Add foreign key for pricing_detail.cat_id -> pricing.id
ALTER TABLE pricing_detail 
ADD CONSTRAINT fk_pricing_detail_category 
FOREIGN KEY (cat_id) REFERENCES pricing(id) 
ON UPDATE CASCADE ON DELETE CASCADE;

-- Add foreign key for pay.employee_id -> employees.employee_id
ALTER TABLE pay 
ADD CONSTRAINT fk_pay_employee 
FOREIGN KEY (employee_id) REFERENCES employees(employee_id) 
ON UPDATE CASCADE ON DELETE CASCADE;

-- Add foreign key for deductions.employee_id -> employees.employee_id
ALTER TABLE deductions 
ADD CONSTRAINT fk_deductions_employee 
FOREIGN KEY (employee_id) REFERENCES employees(employee_id) 
ON UPDATE CASCADE ON DELETE CASCADE;

-- Add foreign key for users.employee_id -> employees.employee_id
ALTER TABLE users 
ADD CONSTRAINT fk_users_employee 
FOREIGN KEY (employee_id) REFERENCES employees(employee_id) 
ON UPDATE CASCADE ON DELETE SET NULL;

-- =====================================================
-- 3. ADD PERFORMANCE INDEXES
-- =====================================================

-- Customer search indexes
CREATE INDEX idx_customers_name ON customers(full_name);
CREATE INDEX idx_customers_phone ON customers(phone_number);
CREATE INDEX idx_customers_created ON customers(created_at);

-- Transaction indexes for reporting
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_customer_date ON transactions(customer_id, transaction_date);

-- Payment indexes
CREATE INDEX idx_payments_method ON payments(method);
CREATE INDEX idx_payments_date ON payments(created_at);
CREATE INDEX idx_payments_reference ON payments(gcash_reference_number);

-- Employee and attendance indexes
CREATE INDEX idx_employees_position ON employees(position);
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_attendance_employee_date ON attendance(employee_id, date);
CREATE INDEX idx_daily_attendance_date ON daily_attendance_summary(date);
CREATE INDEX idx_daily_attendance_employee_date ON daily_attendance_summary(employee_id, date);

-- Expense indexes for reporting
CREATE INDEX idx_expenses_date ON expenses(expense_date);
CREATE INDEX idx_expenses_recorded_by ON expenses(recorded_by);

-- Loyalty points indexes
CREATE INDEX idx_loyalty_status ON loyalty_points(status);
CREATE INDEX idx_loyalty_updated ON loyalty_points(updated_at);

-- Product indexes
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_stock ON products(stock_quantity);

-- =====================================================
-- 4. ADD MISSING NOT NULL CONSTRAINTS
-- =====================================================

-- Update customers table
ALTER TABLE customers 
MODIFY COLUMN full_name VARCHAR(100) NOT NULL,
MODIFY COLUMN created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Update employees table
ALTER TABLE employees 
MODIFY COLUMN full_name VARCHAR(100) NOT NULL;

-- Update transactions table
ALTER TABLE transactions 
MODIFY COLUMN transaction_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
MODIFY COLUMN total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
MODIFY COLUMN amount_paid DECIMAL(10,2) NOT NULL DEFAULT 0.00,
MODIFY COLUMN balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
MODIFY COLUMN status ENUM('PENDING','PARTIAL','PAID','CANCELLED') NOT NULL DEFAULT 'PENDING';

-- Update transaction_details table
ALTER TABLE transaction_details 
MODIFY COLUMN category TEXT NOT NULL,
MODIFY COLUMN weight DECIMAL(5,2) NOT NULL,
MODIFY COLUMN price_per_kg DECIMAL(10,2) NOT NULL;

-- Update payments table
ALTER TABLE payments 
MODIFY COLUMN method VARCHAR(50) NOT NULL,
MODIFY COLUMN transaction_amount DECIMAL(10,2) NOT NULL,
MODIFY COLUMN amount_paid DECIMAL(10,2) NOT NULL,
MODIFY COLUMN created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Update products table
ALTER TABLE products 
MODIFY COLUMN name VARCHAR(100) NOT NULL,
MODIFY COLUMN price DECIMAL(10,2) NOT NULL,
MODIFY COLUMN stock_quantity INT(10) NOT NULL DEFAULT 0,
MODIFY COLUMN created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Update expenses table
ALTER TABLE expenses 
MODIFY COLUMN expense_date DATE NOT NULL,
MODIFY COLUMN amount DECIMAL(10,2) NOT NULL;

-- =====================================================
-- 5. OPTIMIZE DATA TYPES
-- =====================================================

-- Optimize TEXT fields to VARCHAR where appropriate
ALTER TABLE customers 
MODIFY COLUMN phone_number VARCHAR(20),
MODIFY COLUMN address VARCHAR(500);

ALTER TABLE employees 
MODIFY COLUMN position VARCHAR(50),
MODIFY COLUMN phone_number VARCHAR(20);

ALTER TABLE payments 
MODIFY COLUMN method VARCHAR(50),
MODIFY COLUMN gcash_reference_number VARCHAR(100);

ALTER TABLE loyalty_points 
MODIFY COLUMN status VARCHAR(20);

ALTER TABLE users 
MODIFY COLUMN username VARCHAR(50) NOT NULL,
MODIFY COLUMN type VARCHAR(20),
MODIFY COLUMN user_status VARCHAR(20);

ALTER TABLE pricing 
MODIFY COLUMN category VARCHAR(100);

ALTER TABLE attendance 
MODIFY COLUMN status VARCHAR(20),
MODIFY COLUMN remarks VARCHAR(255);

ALTER TABLE daily_attendance_summary 
MODIFY COLUMN remarks VARCHAR(255);

ALTER TABLE deductions 
MODIFY COLUMN deduct_month VARCHAR(20),
MODIFY COLUMN deduct_des VARCHAR(255);

ALTER TABLE profile 
MODIFY COLUMN business_name VARCHAR(200),
MODIFY COLUMN business_owner VARCHAR(100),
MODIFY COLUMN business_address VARCHAR(500),
MODIFY COLUMN business_cell VARCHAR(50),
MODIFY COLUMN business_land VARCHAR(50);

-- =====================================================
-- 6. ADD AUDIT COLUMNS
-- =====================================================

-- Add audit columns to key tables for tracking changes
ALTER TABLE customers 
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN updated_by INT(10) DEFAULT NULL;

ALTER TABLE employees 
ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN updated_by INT(10) DEFAULT NULL;

ALTER TABLE products 
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN updated_by INT(10) DEFAULT NULL;

-- =====================================================
-- 7. CREATE VIEWS FOR COMMON QUERIES
-- =====================================================

-- Customer transaction summary view
CREATE OR REPLACE VIEW customer_summary AS
SELECT 
    c.customer_id,
    c.full_name,
    c.phone_number,
    c.address,
    COUNT(t.transaction_id) as total_transactions,
    SUM(t.total_amount) as total_spent,
    SUM(t.balance) as outstanding_balance,
    COALESCE(lp.points, 0) as loyalty_points,
    MAX(t.transaction_date) as last_transaction_date
FROM customers c
LEFT JOIN transactions t ON c.customer_id = t.customer_id
LEFT JOIN loyalty_points lp ON c.customer_id = lp.customer_id AND lp.status = 'active'
GROUP BY c.customer_id, c.full_name, c.phone_number, c.address, lp.points;

-- Daily sales summary view
CREATE OR REPLACE VIEW daily_sales_summary AS
SELECT 
    DATE(t.transaction_date) as sale_date,
    COUNT(t.transaction_id) as transaction_count,
    SUM(t.total_amount) as total_sales,
    SUM(t.amount_paid) as total_collected,
    SUM(t.balance) as total_outstanding,
    AVG(t.total_amount) as average_transaction
FROM transactions t
WHERE t.status != 'CANCELLED'
GROUP BY DATE(t.transaction_date)
ORDER BY sale_date DESC;

-- Employee attendance summary view
CREATE OR REPLACE VIEW employee_attendance_summary AS
SELECT 
    e.employee_id,
    e.full_name,
    e.position,
    das.date,
    das.time_in,
    das.time_out,
    das.total_hours,
    das.overtime_hours,
    CASE 
        WHEN das.time_in IS NULL THEN 'Absent'
        WHEN das.time_out IS NULL THEN 'Incomplete'
        ELSE 'Present'
    END as attendance_status
FROM employees e
LEFT JOIN daily_attendance_summary das ON e.employee_id = das.employee_id;

-- =====================================================
-- 8. CREATE STORED PROCEDURES FOR COMMON OPERATIONS
-- =====================================================

DELIMITER //

-- Procedure to calculate customer loyalty points
CREATE PROCEDURE CalculateLoyaltyPoints(IN customer_id INT, IN transaction_amount DECIMAL(10,2))
BEGIN
    DECLARE points_to_add INT DEFAULT 0;
    DECLARE current_points INT DEFAULT 0;
    
    -- Calculate points (1 point per 100 pesos spent)
    SET points_to_add = FLOOR(transaction_amount / 100);
    
    -- Get current points
    SELECT COALESCE(points, 0) INTO current_points 
    FROM loyalty_points 
    WHERE customer_id = customer_id AND status = 'active';
    
    -- Update or insert loyalty points
    INSERT INTO loyalty_points (customer_id, points, status, updated_at)
    VALUES (customer_id, current_points + points_to_add, 'active', NOW())
    ON DUPLICATE KEY UPDATE 
        points = points + points_to_add,
        updated_at = NOW();
END //

-- Procedure to update transaction balance
CREATE PROCEDURE UpdateTransactionBalance(IN trans_id INT)
BEGIN
    DECLARE total_amt DECIMAL(10,2) DEFAULT 0;
    DECLARE paid_amt DECIMAL(10,2) DEFAULT 0;
    DECLARE new_balance DECIMAL(10,2) DEFAULT 0;
    DECLARE new_status VARCHAR(20) DEFAULT 'PENDING';
    
    -- Calculate totals
    SELECT 
        COALESCE(SUM(td.subtotal), 0) + COALESCE(SUM(a.trans_subtotal), 0),
        COALESCE(SUM(p.amount_paid), 0)
    INTO total_amt, paid_amt
    FROM transactions t
    LEFT JOIN transaction_details td ON t.transaction_id = td.transaction_id
    LEFT JOIN addons a ON t.transaction_id = a.transaction_id
    LEFT JOIN payments p ON t.transaction_id = p.transaction_id
    WHERE t.transaction_id = trans_id;
    
    -- Calculate balance and status
    SET new_balance = total_amt - paid_amt;
    
    IF new_balance <= 0 THEN
        SET new_status = 'PAID';
        SET new_balance = 0;
    ELSEIF paid_amt > 0 THEN
        SET new_status = 'PARTIAL';
    ELSE
        SET new_status = 'PENDING';
    END IF;
    
    -- Update transaction
    UPDATE transactions 
    SET 
        total_amount = total_amt,
        amount_paid = paid_amt,
        balance = new_balance,
        status = new_status
    WHERE transaction_id = trans_id;
END //

DELIMITER ;

-- =====================================================
-- 9. INSERT DEFAULT DATA
-- =====================================================

-- Insert default business profile if not exists
INSERT IGNORE INTO profile (profileid, business_name, business_owner, business_address, business_cell, business_land)
VALUES (1, 'Super Hero Laundry Squad', 'Business Owner', 'Business Address', '***********', '(02) 123-4567');

-- Insert default pricing categories if not exists
INSERT IGNORE INTO pricing (id, category) VALUES 
(1, 'Wash Only'),
(2, 'Dry Only'), 
(3, 'Wash & Dry'),
(4, 'Premium Service'),
(5, 'Express Service');

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
SELECT 'Database schema improvements completed successfully!' as message;
