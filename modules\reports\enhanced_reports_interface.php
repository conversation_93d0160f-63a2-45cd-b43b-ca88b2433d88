<?php
session_start();
require_once '../../config/database.php';
require_once 'advanced_reports.php';

// Initialize report generator
$reportGenerator = new AdvancedReportGenerator($conn);

// Handle report generation requests
$reportType = $_GET['type'] ?? '';
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-d'); // Today
$groupBy = $_GET['group_by'] ?? 'daily';
$export = $_GET['export'] ?? '';

$reportData = [];
$reportTitle = '';

if ($reportType) {
    switch ($reportType) {
        case 'sales':
            $reportData = $reportGenerator->generateSalesReport($startDate, $endDate, $groupBy);
            $reportTitle = 'Sales Analytics Report';
            break;
        case 'customers':
            $reportData = $reportGenerator->generateCustomerAnalytics($startDate, $endDate);
            $reportTitle = 'Customer Analytics Report';
            break;
        case 'services':
            $reportData = $reportGenerator->generateServicePerformanceReport($startDate, $endDate);
            $reportTitle = 'Service Performance Report';
            break;
        case 'financial':
            $reportData = $reportGenerator->generateFinancialSummary($startDate, $endDate);
            $reportTitle = 'Financial Summary Report';
            break;
        case 'employees':
            $reportData = $reportGenerator->generateEmployeePerformanceReport($startDate, $endDate);
            $reportTitle = 'Employee Performance Report';
            break;
        case 'payments':
            $reportData = $reportGenerator->generatePaymentMethodAnalysis($startDate, $endDate);
            $reportTitle = 'Payment Method Analysis';
            break;
    }
    
    // Handle CSV export
    if ($export === 'csv' && !empty($reportData)) {
        $filename = strtolower(str_replace(' ', '_', $reportTitle)) . '_' . date('Y-m-d');
        $reportGenerator->exportToCSV($reportData, $filename);
    }
}

// Fetch business profile
$sql = "SELECT * FROM profile LIMIT 1";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC);
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $business_name; ?> - Advanced Reports</title>
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #4B89DC;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            font-size: 14px;
        }
        
        .reports-header {
            background: linear-gradient(135deg, var(--primary-color), #5D9CEC);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .report-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
            cursor: pointer;
        }
        
        .report-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        
        .report-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .filters-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
        }
        
        .results-section {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }
        
        .results-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
        }
        
        .table-responsive {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: var(--card-shadow);
            margin-bottom: 1rem;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .export-buttons {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .segment-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .segment-vip { background-color: rgba(255, 215, 0, 0.2); color: #b8860b; }
        .segment-premium { background-color: rgba(138, 43, 226, 0.2); color: #8a2be2; }
        .segment-regular { background-color: rgba(0, 123, 255, 0.2); color: #007bff; }
        .segment-new { background-color: rgba(40, 167, 69, 0.2); color: #28a745; }
        
        .status-active { background-color: rgba(40, 167, 69, 0.2); color: #28a745; }
        .status-at-risk { background-color: rgba(255, 193, 7, 0.2); color: #ffc107; }
        .status-inactive { background-color: rgba(220, 53, 69, 0.2); color: #dc3545; }
    </style>
</head>
<body>
    <!-- Reports Header -->
    <div class="reports-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Advanced Business Reports
                    </h1>
                    <p class="mb-0 opacity-75">Comprehensive analytics and business intelligence</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="../../pages/reports.php" class="btn btn-light">
                        <i class="fas fa-arrow-left me-2"></i>Back to Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if (!$reportType): ?>
        <!-- Report Selection Grid -->
        <div class="row g-4">
            <div class="col-md-4">
                <div class="report-card text-center" onclick="selectReport('sales')">
                    <div class="report-icon text-success">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h5>Sales Analytics</h5>
                    <p class="text-muted">Comprehensive sales performance analysis with trends and insights</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="report-card text-center" onclick="selectReport('customers')">
                    <div class="report-icon text-primary">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5>Customer Analytics</h5>
                    <p class="text-muted">Customer segmentation, lifetime value, and behavior analysis</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="report-card text-center" onclick="selectReport('services')">
                    <div class="report-icon text-info">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h5>Service Performance</h5>
                    <p class="text-muted">Analysis of service categories and performance metrics</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="report-card text-center" onclick="selectReport('financial')">
                    <div class="report-icon text-warning">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h5>Financial Summary</h5>
                    <p class="text-muted">Revenue, expenses, profit analysis and financial health</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="report-card text-center" onclick="selectReport('employees')">
                    <div class="report-icon text-secondary">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h5>Employee Performance</h5>
                    <p class="text-muted">Attendance, productivity, and payroll analysis</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="report-card text-center" onclick="selectReport('payments')">
                    <div class="report-icon text-danger">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h5>Payment Analysis</h5>
                    <p class="text-muted">Payment method preferences and transaction analysis</p>
                </div>
            </div>
        </div>
        <?php else: ?>
        <!-- Report Filters and Results -->
        <div class="filters-section">
            <form method="GET" class="row g-3 align-items-end">
                <input type="hidden" name="type" value="<?php echo $reportType; ?>">
                
                <div class="col-md-3">
                    <label class="form-label">Start Date</label>
                    <input type="date" name="start_date" class="form-control" value="<?php echo $startDate; ?>" required>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">End Date</label>
                    <input type="date" name="end_date" class="form-control" value="<?php echo $endDate; ?>" required>
                </div>
                
                <?php if ($reportType === 'sales'): ?>
                <div class="col-md-3">
                    <label class="form-label">Group By</label>
                    <select name="group_by" class="form-select">
                        <option value="daily" <?php echo $groupBy === 'daily' ? 'selected' : ''; ?>>Daily</option>
                        <option value="weekly" <?php echo $groupBy === 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                        <option value="monthly" <?php echo $groupBy === 'monthly' ? 'selected' : ''; ?>>Monthly</option>
                    </select>
                </div>
                <?php endif; ?>
                
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="location.href='?'">
                        <i class="fas fa-arrow-left me-2"></i>Back
                    </button>
                </div>
            </form>
        </div>

        <?php if (!empty($reportData)): ?>
        <!-- Report Results -->
        <div class="results-section">
            <div class="results-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0"><?php echo $reportTitle; ?></h5>
                    <small>Period: <?php echo date('M d, Y', strtotime($startDate)); ?> - <?php echo date('M d, Y', strtotime($endDate)); ?></small>
                </div>
                <div class="export-buttons">
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['export' => 'csv'])); ?>"
                       class="btn btn-success btn-sm">
                        <i class="fas fa-file-csv me-1"></i>Export CSV
                    </a>
                    <button onclick="window.print()" class="btn btn-info btn-sm">
                        <i class="fas fa-print me-1"></i>Print
                    </button>
                </div>
            </div>

            <div class="p-3">
                <?php if ($reportType === 'financial'): ?>
                    <!-- Financial Summary Cards -->
                    <div class="row g-3 mb-4">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value text-primary">₱<?php echo number_format($reportData['revenue']['total_revenue'], 2); ?></div>
                                <div class="metric-label">Total Revenue</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value text-success">₱<?php echo number_format($reportData['revenue']['total_collected'], 2); ?></div>
                                <div class="metric-label">Total Collected</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value text-danger">₱<?php echo number_format($reportData['expenses']['total_expenses'], 2); ?></div>
                                <div class="metric-label">Total Expenses</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value <?php echo $reportData['net_profit'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    ₱<?php echo number_format($reportData['net_profit'], 2); ?>
                                </div>
                                <div class="metric-label">Net Profit</div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="metric-card">
                                <div class="metric-value text-info"><?php echo number_format($reportData['profit_margin'], 1); ?>%</div>
                                <div class="metric-label">Profit Margin</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-card">
                                <div class="metric-value text-warning"><?php echo number_format($reportData['collection_rate'], 1); ?>%</div>
                                <div class="metric-label">Collection Rate</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-card">
                                <div class="metric-value text-secondary"><?php echo number_format($reportData['revenue']['total_transactions']); ?></div>
                                <div class="metric-label">Total Transactions</div>
                            </div>
                        </div>
                    </div>

                <?php else: ?>
                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <?php if (!empty($reportData)): ?>
                                        <?php foreach (array_keys($reportData[0]) as $header): ?>
                                            <th><?php echo ucwords(str_replace('_', ' ', $header)); ?></th>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData as $row): ?>
                                <tr>
                                    <?php foreach ($row as $key => $value): ?>
                                        <td>
                                            <?php if (strpos($key, 'amount') !== false || strpos($key, 'revenue') !== false || strpos($key, 'spent') !== false || strpos($key, 'paid') !== false || strpos($key, 'balance') !== false || strpos($key, 'pay') !== false): ?>
                                                ₱<?php echo number_format($value, 2); ?>
                                            <?php elseif (strpos($key, 'percentage') !== false || strpos($key, 'rate') !== false): ?>
                                                <?php echo number_format($value, 1); ?>%
                                            <?php elseif ($key === 'customer_segment'): ?>
                                                <span class="segment-badge segment-<?php echo strtolower($value); ?>">
                                                    <?php echo $value; ?>
                                                </span>
                                            <?php elseif ($key === 'customer_status'): ?>
                                                <span class="segment-badge status-<?php echo strtolower(str_replace(' ', '-', $value)); ?>">
                                                    <?php echo $value; ?>
                                                </span>
                                            <?php elseif (strpos($key, 'date') !== false): ?>
                                                <?php echo $value ? date('M d, Y', strtotime($value)) : 'N/A'; ?>
                                            <?php else: ?>
                                                <?php echo htmlspecialchars($value); ?>
                                            <?php endif; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        <?php endif; ?>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function selectReport(type) {
            window.location.href = '?type=' + type;
        }

        // Print styles
        const printStyles = `
            @media print {
                .reports-header, .filters-section, .export-buttons { display: none !important; }
                .results-section { box-shadow: none !important; border: 1px solid #ddd; }
                body { background: white !important; }
                .table { font-size: 12px; }
            }
        `;

        const styleSheet = document.createElement("style");
        styleSheet.type = "text/css";
        styleSheet.innerText = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
