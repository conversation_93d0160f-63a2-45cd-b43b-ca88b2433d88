<?php
session_start();
require('../fpdf/fpdf.php');
include('../config/database.php');

if(!isset($_SESSION['employee_id'])) {
    header('Location: ../index.php');
    exit();
}

// Fetch business profile
$sql = "SELECT * FROM profile";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single row

// Ensure business_name is retrieved correctly
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad"; 
$business_owner = $profile['business_owner'] ??""; 
$business_address = $profile['business_address'] ??""; 
$business_cell = $profile['business_cell'] ??""; 
$business_land = $profile['business_land'] ??""; 

// Custom PDF class with header and footer
class PDF extends FPDF {
    function Header() {
        // Logo (replace path with your logo)
        // $this->Image('logo.png', 10, 10, 30);
        
        // Company name
        $this->SetFont('Arial', 'B', 18);
        $this->Cell(0, 10, $GLOBALS['business_name'], 0, 1, 'C');
        
        // Address
        $this->SetFont('Arial', '', 8);
        $this->Cell(0, 5, $GLOBALS['business_address'], 0, 1, 'C');
        $this->Cell(0, 5, 'Phone: ' . $GLOBALS['business_cell'] . ' | Landline: ' . $GLOBALS['business_land'] . ' ', 0, 1, 'C');
        $this->Cell(0, 5, 'Owed and Managed by: ' . $GLOBALS['business_owner'] .  ' ', 0, 1, 'C');
        
        // Line break
        $this->Ln(10);
    }
}

if(isset($_GET['start_date']) && isset($_GET['end_date'])) {
    $start_date = $_GET['start_date'];
    $end_date = $_GET['end_date'];
    
    $pdf = new PDF('P', 'mm', 'Letter');
    $pdf->AliasNbPages();
    $pdf->AddPage();
    
    // Title
    $pdf->SetFont('Arial', 'B', 16);
    $pdf->Cell(0, 10, 'MONTHLY RECEIVABLES REPORT', 0, 1, 'C');
    
    // Report Period
    $pdf->SetFillColor(240, 240, 240);
    $pdf->Rect(10, $pdf->GetY(), 195, 15, 'F');
    
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(30, 10, 'Period:', 0, 0);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(0, 10, date('F d', strtotime($start_date)).' - '.date('F d, Y', strtotime($end_date)), 0, 1);
    
    // Table header
    $pdf->Ln(10);
    $pdf->SetFillColor(50, 50, 50);
    $pdf->SetTextColor(255, 255, 255);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(40, 8, 'Transaction ID', 1, 0, 'C', true);
    $pdf->Cell(50, 8, 'Customer Name', 1, 0, 'C', true);
    $pdf->Cell(35, 8, 'Total Amount', 1, 0, 'C', true);
    $pdf->Cell(35, 8, 'Amount Paid', 1, 0, 'C', true);
    $pdf->Cell(35, 8, 'Remaining Balance', 1, 1, 'C', true);

    // Reset text color for data
    $pdf->SetTextColor(0, 0, 0);
    $pdf->SetFont('Arial', '', 9);

    // Get receivables data
    $sql = "SELECT p.transaction_id, 
                   SUM(p.amount_paid) as total_amount_paid, 
                   MAX(p.transaction_amount) as transaction_amount, 
                   MAX(p.transaction_amount) - SUM(p.amount_paid) as remaining_balance, 
                   c.full_name 
            FROM payments p 
            JOIN transactions t ON p.transaction_id = t.transaction_id 
            JOIN customers c ON t.customer_id = c.customer_id 
            WHERE 1=1 
            AND NOT EXISTS (
                SELECT 1 FROM payments pm 
                WHERE pm.transaction_id = p.transaction_id 
                AND pm.method = 'loyalty' 
                AND pm.amount_paid <= 0
            )";

    if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
        $sql .= " AND DATE(t.transaction_date) >= '" . $_GET['start_date'] . "'";
    }

    if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
        $sql .= " AND DATE(t.transaction_date) <= '" . $_GET['end_date'] . "'";
    }

    $sql .= " GROUP BY p.transaction_id, c.full_name 
              HAVING remaining_balance > 0 
              ORDER BY p.transaction_id";

    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $receivable_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach($receivable_records as $row) {
        $pdf->Cell(40, 7, $row['transaction_id'], 1, 0, 'C');
        $pdf->Cell(50, 7, $row['full_name'], 1, 0, 'C');
        $pdf->Cell(35, 7, 'PHP '.number_format($row['transaction_amount'], 2), 1, 0, 'R');
        $pdf->Cell(35, 7, 'PHP '.number_format($row['total_amount_paid'], 2), 1, 0, 'R');
        $pdf->Cell(35, 7, 'PHP '.number_format($row['remaining_balance'], 2), 1, 1, 'R');
    }
    // Calculate and display total receivables
    $total_receivables = array_sum(array_column($receivable_records, 'remaining_balance'));
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(160, 7, 'Total Receivables:', 1, 0, 'R');
    $pdf->Cell(35, 7, 'PHP '.number_format($total_receivables, 2), 1, 1, 'R');
    // Signature section
    $pdf->Ln(20);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(95, 5, 'Prepared by:', 0, 0);
    $pdf->Cell(95, 5, 'Approved by:', 0, 1);

    $pdf->Ln(15);
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(95, 5, '_____________________', 0, 0, 'C');
    $pdf->Cell(95, 5, '_____________________', 0, 1, 'C');
    $pdf->SetFont('Arial', '', 10);
    $pdf->Cell(95, 5, 'Finance Officer', 0, 0, 'C');
    $pdf->Cell(95, 5, 'KENNETH LEONIDA', 0, 1, 'C');
    $pdf->Cell(95, 5, '', 0, 0, 'C');
    $pdf->Cell(95, 5, 'Owner', 0, 1, 'C');

    // Output PDF
    $filename = isset($_GET['filename']) ? $_GET['filename'] : 'receivables_report';
    $pdf->Output($filename.'.pdf', 'I');
    exit();
}
?>

