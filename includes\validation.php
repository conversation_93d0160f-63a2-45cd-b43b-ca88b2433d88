<?php
/**
 * Comprehensive Validation System
 * Handles all validation for transaction operations
 */

class TransactionValidator {
    private $conn;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
    }
    
    /**
     * Validate transaction item data
     */
    public function validateTransactionItem($data) {
        $errors = [];
        
        // Validate category
        if (empty($data['category']) || !is_numeric($data['category'])) {
            $errors[] = "Valid category is required";
        } else {
            // Check if category exists
            $sql = "SELECT id FROM pricing WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$data['category']]);
            if (!$stmt->fetch()) {
                $errors[] = "Selected category does not exist";
            }
        }
        
        // Validate weight
        if (empty($data['weight']) || !is_numeric($data['weight']) || $data['weight'] <= 0) {
            $errors[] = "Weight must be a positive number";
        } elseif ($data['weight'] > 1000) {
            $errors[] = "Weight cannot exceed 1000 kg";
        }
        
        // Validate remarks (optional but check length)
        if (!empty($data['item_remarks']) && strlen($data['item_remarks']) > 500) {
            $errors[] = "Remarks cannot exceed 500 characters";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Validate addon product data
     */
    public function validateAddonProduct($data) {
        $errors = [];
        
        // Validate product ID
        if (empty($data['product_id']) || !is_numeric($data['product_id'])) {
            $errors[] = "Valid product is required";
        } else {
            // Check product exists and has sufficient stock
            $sql = "SELECT stock_quantity, name, price FROM products WHERE product_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$data['product_id']]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$product) {
                $errors[] = "Selected product does not exist";
            } else {
                // Validate quantity
                if (empty($data['quantity']) || !is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                    $errors[] = "Quantity must be a positive number";
                } elseif ($data['quantity'] > $product['stock_quantity']) {
                    $errors[] = "Insufficient stock. Available: " . $product['stock_quantity'];
                } elseif ($data['quantity'] > 1000) {
                    $errors[] = "Quantity cannot exceed 1000";
                }
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'product' => $product ?? null
        ];
    }
    
    /**
     * Validate payment data
     */
    public function validatePayment($data, $customer_id, $transaction_balance) {
        $errors = [];
        
        // Validate payment method
        $valid_methods = ['cash', 'gcash', 'credit', 'points'];
        if (empty($data['payment_method']) || !in_array($data['payment_method'], $valid_methods)) {
            $errors[] = "Valid payment method is required";
        }
        
        // Method-specific validations
        if ($data['payment_method'] === 'gcash') {
            if (empty($data['gcash_reference'])) {
                $errors[] = "GCash reference number is required";
            } elseif (strlen($data['gcash_reference']) < 5) {
                $errors[] = "GCash reference number must be at least 5 characters";
            }
        }
        
        if ($data['payment_method'] === 'points') {
            if (empty($data['points_used']) || !is_numeric($data['points_used']) || $data['points_used'] <= 0) {
                $errors[] = "Points amount must be a positive number";
            } else {
                // Check available points
                $sql = "SELECT SUM(points) as available_points 
                        FROM loyalty_points 
                        WHERE customer_id = ? AND status = 'AVAILABLE'";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$customer_id]);
                $available_points = $stmt->fetch(PDO::FETCH_ASSOC)['available_points'] ?? 0;
                
                if ($data['points_used'] > $available_points) {
                    $errors[] = "Insufficient points. Available: " . $available_points;
                }
            }
        } else {
            // Validate amount paid for non-points payments
            if (empty($data['amount_paid']) || !is_numeric($data['amount_paid']) || $data['amount_paid'] <= 0) {
                $errors[] = "Payment amount must be a positive number";
            } elseif ($data['amount_paid'] > 1000000) {
                $errors[] = "Payment amount cannot exceed ₱1,000,000";
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Validate transaction ID
     */
    public function validateTransactionId($transaction_id) {
        if (empty($transaction_id) || !is_numeric($transaction_id)) {
            return [
                'valid' => false,
                'error' => "Valid transaction ID is required"
            ];
        }
        
        $sql = "SELECT transaction_id FROM transactions WHERE transaction_id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$transaction_id]);
        
        if (!$stmt->fetch()) {
            return [
                'valid' => false,
                'error' => "Transaction not found"
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Sanitize input data
     */
    public function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate pricing configuration (using actual table name)
     */
    public function validatePricingExists($category_id, $weight) {
        $sql = "SELECT pd.price, pd.kilogram, pd.num_load
                FROM pricing p
                JOIN pricing_detail pd ON p.id = pd.cat_id
                WHERE p.id = ? AND pd.kilogram <= ?
                ORDER BY pd.kilogram DESC
                LIMIT 1";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$category_id, $weight]);
        $pricing = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$pricing) {
            return [
                'valid' => false,
                'error' => "No pricing configuration found for this category and weight"
            ];
        }

        return [
            'valid' => true,
            'pricing' => $pricing
        ];
    }
    
    /**
     * Check for duplicate transactions (prevent double submission)
     */
    public function checkDuplicateSubmission($transaction_id, $action_type, $data_hash) {
        // This could be implemented to prevent duplicate form submissions
        // For now, return valid
        return ['valid' => true];
    }
    
    /**
     * Validate business rules (backward compatible)
     */
    public function validateBusinessRules($transaction_id, $action) {
        $errors = [];

        // Check if new schema exists
        $status_check = $this->conn->query("SHOW COLUMNS FROM transactions LIKE 'status'");
        $has_status_column = $status_check->rowCount() > 0;

        if ($has_status_column) {
            // New schema with status and balance columns
            $sql = "SELECT status, balance FROM transactions WHERE transaction_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$transaction_id]);
            $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$transaction) {
                $errors[] = "Transaction not found";
                return ['valid' => false, 'errors' => $errors];
            }

            // Business rule validations based on action
            switch ($action) {
                case 'add_item':
                    if ($transaction['status'] === 'PAID') {
                        $errors[] = "Cannot add items to a fully paid transaction";
                    }
                    break;

                case 'add_product':
                    if ($transaction['status'] === 'PAID') {
                        $errors[] = "Cannot add products to a fully paid transaction";
                    }
                    break;

                case 'process_payment':
                    if ($transaction['balance'] <= 0) {
                        $errors[] = "Transaction is already fully paid";
                    }
                    break;
            }
        } else {
            // Old schema - calculate balance manually
            $sql = "SELECT transaction_id FROM transactions WHERE transaction_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$transaction_id]);
            $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$transaction) {
                $errors[] = "Transaction not found";
                return ['valid' => false, 'errors' => $errors];
            }

            // For old schema, calculate if transaction is paid
            if ($action === 'process_payment') {
                // Calculate total amount
                $total_sql = "SELECT
                                COALESCE(SUM(td.subtotal), 0) + COALESCE(SUM(a.trans_subtotal), 0) as total_amount
                              FROM transaction_details td
                              LEFT JOIN addons a ON td.transaction_id = a.transaction_id
                              WHERE td.transaction_id = ?";
                $stmt = $this->conn->prepare($total_sql);
                $stmt->execute([$transaction_id]);
                $total_amount = $stmt->fetch(PDO::FETCH_ASSOC)['total_amount'] ?? 0;

                // Calculate total payments
                $payment_sql = "SELECT COALESCE(SUM(amount_paid), 0) as total_paid
                               FROM payments WHERE transaction_id = ?";
                $stmt = $this->conn->prepare($payment_sql);
                $stmt->execute([$transaction_id]);
                $total_paid = $stmt->fetch(PDO::FETCH_ASSOC)['total_paid'] ?? 0;

                $balance = $total_amount - $total_paid;

                if ($balance <= 0) {
                    $errors[] = "Transaction is already fully paid";
                }
            }

            // For old schema, we're more lenient with add_item and add_product
            // since we don't have a reliable status field
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
?>
