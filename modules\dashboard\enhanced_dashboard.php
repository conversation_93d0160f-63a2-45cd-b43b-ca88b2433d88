<?php
session_start();
require_once '../../config/database.php';
require_once 'dashboard_data.php';

// Initialize dashboard data provider
$dashboardData = new DashboardDataProvider($conn);

// Get current period from request or default to today
$period = $_GET['period'] ?? 'today';

// Fetch dashboard data
$kpis = $dashboardData->getKPIs($period);
$salesTrend = $dashboardData->getSalesTrend(30);
$expenseTrend = $dashboardData->getExpenseTrend(30);
$topCustomers = $dashboardData->getTopCustomers(5);
$servicePerformance = $dashboardData->getServicePerformance();
$paymentStats = $dashboardData->getPaymentMethodStats();
$recentTransactions = $dashboardData->getRecentTransactions(8);
$employeeAttendance = $dashboardData->getEmployeeAttendance();
$lowStockAlerts = $dashboardData->getLowStockAlerts(10);
$outstandingReceivables = $dashboardData->getOutstandingReceivables();
$comparisonData = $dashboardData->getComparisonData('this_month', 'last_month');

// Fetch business profile
$sql = "SELECT * FROM profile LIMIT 1";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC);
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $business_name; ?> - Enhanced Dashboard</title>
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #4B89DC;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --card-hover-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            font-size: 14px;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, var(--primary-color), #5D9CEC);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .kpi-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }
        
        .kpi-card:hover {
            box-shadow: var(--card-hover-shadow);
            transform: translateY(-2px);
        }
        
        .kpi-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .kpi-label {
            color: #6c757d;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .kpi-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        
        .trend-indicator {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .trend-up {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }
        
        .trend-down {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
        }
        
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }
        
        .table-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }
        
        .alert-card {
            border-left: 4px solid var(--warning-color);
            background-color: rgba(255, 193, 7, 0.1);
        }
        
        .period-selector {
            background: white;
            border-radius: 8px;
            padding: 0.5rem;
            box-shadow: var(--card-shadow);
        }
        
        .period-btn {
            border: none;
            background: transparent;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .period-btn.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-paid { background-color: rgba(40, 167, 69, 0.1); color: var(--success-color); }
        .status-pending { background-color: rgba(255, 193, 7, 0.1); color: var(--warning-color); }
        .status-partial { background-color: rgba(23, 162, 184, 0.1); color: var(--info-color); }
        .status-cancelled { background-color: rgba(220, 53, 69, 0.1); color: var(--danger-color); }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        @media (max-width: 768px) {
            .chart-grid {
                grid-template-columns: 1fr;
            }
            .metric-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            border: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            font-size: 1.25rem;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background-color: #3d7bd6;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        <?php echo $business_name; ?> Dashboard
                    </h1>
                    <p class="mb-0 opacity-75">Real-time business analytics and insights</p>
                </div>
                <div class="col-md-6 text-end">
                    <div class="period-selector d-inline-block">
                        <button class="period-btn <?php echo $period === 'today' ? 'active' : ''; ?>" 
                                onclick="changePeriod('today')">Today</button>
                        <button class="period-btn <?php echo $period === 'this_week' ? 'active' : ''; ?>" 
                                onclick="changePeriod('this_week')">This Week</button>
                        <button class="period-btn <?php echo $period === 'this_month' ? 'active' : ''; ?>" 
                                onclick="changePeriod('this_month')">This Month</button>
                        <button class="period-btn <?php echo $period === 'this_year' ? 'active' : ''; ?>" 
                                onclick="changePeriod('this_year')">This Year</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- KPI Metrics Grid -->
        <div class="metric-grid">
            <!-- Total Revenue -->
            <div class="kpi-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="kpi-value text-success">₱<?php echo number_format($kpis['total_revenue'], 2); ?></div>
                        <div class="kpi-label">Total Revenue</div>
                        <?php if (isset($comparisonData['total_revenue'])): ?>
                            <span class="trend-indicator trend-<?php echo $comparisonData['total_revenue']['trend']; ?>">
                                <i class="fas fa-arrow-<?php echo $comparisonData['total_revenue']['trend'] === 'up' ? 'up' : 'down'; ?>"></i>
                                <?php echo abs(round($comparisonData['total_revenue']['change'], 1)); ?>%
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="kpi-icon text-success">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>

            <!-- Total Collected -->
            <div class="kpi-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="kpi-value text-primary">₱<?php echo number_format($kpis['total_collected'], 2); ?></div>
                        <div class="kpi-label">Total Collected</div>
                        <?php if (isset($comparisonData['total_collected'])): ?>
                            <span class="trend-indicator trend-<?php echo $comparisonData['total_collected']['trend']; ?>">
                                <i class="fas fa-arrow-<?php echo $comparisonData['total_collected']['trend'] === 'up' ? 'up' : 'down'; ?>"></i>
                                <?php echo abs(round($comparisonData['total_collected']['change'], 1)); ?>%
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="kpi-icon text-primary">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>

            <!-- Outstanding Balance -->
            <div class="kpi-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="kpi-value text-warning">₱<?php echo number_format($kpis['outstanding_balance'], 2); ?></div>
                        <div class="kpi-label">Outstanding Balance</div>
                        <?php if (isset($comparisonData['outstanding_balance'])): ?>
                            <span class="trend-indicator trend-<?php echo $comparisonData['outstanding_balance']['trend'] === 'up' ? 'down' : 'up'; ?>">
                                <i class="fas fa-arrow-<?php echo $comparisonData['outstanding_balance']['trend'] === 'up' ? 'up' : 'down'; ?>"></i>
                                <?php echo abs(round($comparisonData['outstanding_balance']['change'], 1)); ?>%
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="kpi-icon text-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>

            <!-- Total Transactions -->
            <div class="kpi-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="kpi-value text-info"><?php echo number_format($kpis['total_transactions']); ?></div>
                        <div class="kpi-label">Total Transactions</div>
                        <?php if (isset($comparisonData['total_transactions'])): ?>
                            <span class="trend-indicator trend-<?php echo $comparisonData['total_transactions']['trend']; ?>">
                                <i class="fas fa-arrow-<?php echo $comparisonData['total_transactions']['trend'] === 'up' ? 'up' : 'down'; ?>"></i>
                                <?php echo abs(round($comparisonData['total_transactions']['change'], 1)); ?>%
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="kpi-icon text-info">
                        <i class="fas fa-receipt"></i>
                    </div>
                </div>
            </div>

            <!-- Unique Customers -->
            <div class="kpi-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="kpi-value text-secondary"><?php echo number_format($kpis['unique_customers']); ?></div>
                        <div class="kpi-label">Unique Customers</div>
                        <?php if (isset($comparisonData['unique_customers'])): ?>
                            <span class="trend-indicator trend-<?php echo $comparisonData['unique_customers']['trend']; ?>">
                                <i class="fas fa-arrow-<?php echo $comparisonData['unique_customers']['trend'] === 'up' ? 'up' : 'down'; ?>"></i>
                                <?php echo abs(round($comparisonData['unique_customers']['change'], 1)); ?>%
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="kpi-icon text-secondary">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>

            <!-- Net Profit -->
            <div class="kpi-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="kpi-value <?php echo $kpis['net_profit'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                            ₱<?php echo number_format($kpis['net_profit'], 2); ?>
                        </div>
                        <div class="kpi-label">Net Profit</div>
                        <?php if (isset($comparisonData['net_profit'])): ?>
                            <span class="trend-indicator trend-<?php echo $comparisonData['net_profit']['trend']; ?>">
                                <i class="fas fa-arrow-<?php echo $comparisonData['net_profit']['trend'] === 'up' ? 'up' : 'down'; ?>"></i>
                                <?php echo abs(round($comparisonData['net_profit']['change'], 1)); ?>%
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="kpi-icon <?php echo $kpis['net_profit'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="chart-grid">
            <!-- Sales Trend Chart -->
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-area me-2"></i>Sales Trend (Last 30 Days)
                </h5>
                <canvas id="salesTrendChart" height="300"></canvas>
            </div>

            <!-- Service Performance Chart -->
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>Service Performance
                </h5>
                <canvas id="servicePerformanceChart" height="300"></canvas>
            </div>
        </div>

        <!-- Data Tables Section -->
        <div class="row">
            <!-- Recent Transactions -->
            <div class="col-lg-8 mb-4">
                <div class="table-container">
                    <div class="table-header">
                        <i class="fas fa-clock me-2"></i>Recent Transactions
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentTransactions as $transaction): ?>
                                <tr>
                                    <td><strong>#<?php echo $transaction['transaction_id']; ?></strong></td>
                                    <td><?php echo htmlspecialchars($transaction['customer_name']); ?></td>
                                    <td><?php echo date('M d, Y H:i', strtotime($transaction['transaction_date'])); ?></td>
                                    <td>₱<?php echo number_format($transaction['total_amount'], 2); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo strtolower($transaction['status']); ?>">
                                            <?php echo $transaction['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($transaction['balance'] > 0): ?>
                                            <span class="text-warning">₱<?php echo number_format($transaction['balance'], 2); ?></span>
                                        <?php else: ?>
                                            <span class="text-success">₱0.00</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Top Customers -->
            <div class="col-lg-4 mb-4">
                <div class="table-container">
                    <div class="table-header">
                        <i class="fas fa-star me-2"></i>Top Customers
                    </div>
                    <div class="p-3">
                        <?php foreach ($topCustomers as $index => $customer): ?>
                        <div class="d-flex align-items-center mb-3 <?php echo $index < count($topCustomers) - 1 ? 'border-bottom pb-3' : ''; ?>">
                            <div class="me-3">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 40px; height: 40px; font-weight: 600;">
                                    <?php echo $index + 1; ?>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold"><?php echo htmlspecialchars($customer['full_name']); ?></div>
                                <div class="text-muted small">
                                    <?php echo $customer['transaction_count']; ?> transactions
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="fw-semibold text-success">₱<?php echo number_format($customer['total_spent'], 2); ?></div>
                                <?php if ($customer['outstanding_balance'] > 0): ?>
                                    <div class="text-warning small">₱<?php echo number_format($customer['outstanding_balance'], 2); ?> due</div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts and Notifications -->
        <?php if (!empty($lowStockAlerts) || !empty($outstandingReceivables)): ?>
        <div class="row">
            <!-- Low Stock Alerts -->
            <?php if (!empty($lowStockAlerts)): ?>
            <div class="col-lg-6 mb-4">
                <div class="alert-card card">
                    <div class="card-header bg-warning text-dark">
                        <i class="fas fa-exclamation-triangle me-2"></i>Low Stock Alerts
                    </div>
                    <div class="card-body">
                        <?php foreach ($lowStockAlerts as $product): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                <small class="text-muted d-block">₱<?php echo number_format($product['price'], 2); ?></small>
                            </div>
                            <span class="badge bg-warning text-dark">
                                <?php echo $product['stock_quantity']; ?> left
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Outstanding Receivables -->
            <?php if (!empty($outstandingReceivables)): ?>
            <div class="col-lg-6 mb-4">
                <div class="alert-card card">
                    <div class="card-header bg-danger text-white">
                        <i class="fas fa-file-invoice-dollar me-2"></i>Outstanding Receivables
                    </div>
                    <div class="card-body">
                        <?php foreach (array_slice($outstandingReceivables, 0, 5) as $receivable): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong><?php echo htmlspecialchars($receivable['customer_name']); ?></strong>
                                <small class="text-muted d-block">
                                    Transaction #<?php echo $receivable['transaction_id']; ?>
                                    (<?php echo $receivable['days_overdue']; ?> days)
                                </small>
                            </div>
                            <span class="badge bg-danger">
                                ₱<?php echo number_format($receivable['balance'], 2); ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="location.reload()" title="Refresh Dashboard">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Period selector functionality
        function changePeriod(period) {
            window.location.href = '?period=' + period;
        }

        // Sales Trend Chart
        const salesTrendCtx = document.getElementById('salesTrendChart').getContext('2d');
        const salesTrendData = <?php echo json_encode($salesTrend); ?>;

        new Chart(salesTrendCtx, {
            type: 'line',
            data: {
                labels: salesTrendData.map(item => {
                    const date = new Date(item.date);
                    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                }),
                datasets: [{
                    label: 'Daily Sales',
                    data: salesTrendData.map(item => parseFloat(item.daily_sales)),
                    borderColor: '#4B89DC',
                    backgroundColor: 'rgba(75, 137, 220, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Daily Collected',
                    data: salesTrendData.map(item => parseFloat(item.daily_collected)),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₱' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Service Performance Chart
        const servicePerformanceCtx = document.getElementById('servicePerformanceChart').getContext('2d');
        const servicePerformanceData = <?php echo json_encode($servicePerformance); ?>;

        new Chart(servicePerformanceCtx, {
            type: 'doughnut',
            data: {
                labels: servicePerformanceData.map(item => item.category),
                datasets: [{
                    data: servicePerformanceData.map(item => parseFloat(item.total_revenue)),
                    backgroundColor: [
                        '#4B89DC',
                        '#28a745',
                        '#ffc107',
                        '#dc3545',
                        '#17a2b8',
                        '#6f42c1',
                        '#fd7e14'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = '₱' + context.parsed.toLocaleString();
                                return label + ': ' + value;
                            }
                        }
                    }
                }
            }
        });

        // Auto-refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
