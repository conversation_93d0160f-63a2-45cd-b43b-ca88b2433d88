<?php
include_once '../config/database.php';

// Get sales records for selected date range
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

$sales_records = [];
$total_sales = 0;

if ($start_date && $end_date) {
    // Get daily sales records
    $sql = "SELECT 
                DATE(t.transaction_date) as sale_date,
                SUM(td.subtotal) as daily_total
            FROM transactions t
            INNER JOIN transaction_details td ON t.transaction_id = td.transaction_id 
            WHERE DATE(t.transaction_date) BETWEEN ? AND ?
            GROUP BY DATE(t.transaction_date)
            ORDER BY DATE(t.transaction_date)";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$start_date, $end_date]);
    $sales_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate total sales
    $sql = "SELECT SUM(td.subtotal) as total 
            FROM transactions t
            INNER JOIN transaction_details td ON t.transaction_id = td.transaction_id 
            WHERE DATE(t.transaction_date) BETWEEN ? AND ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$start_date, $end_date]);
    $total_sales = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Monthly Sales</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <!-- Report Header Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h2 class="card-title h4 mb-4">
                    <i class="fa-solid fa-chart-line me-2"></i>Monthly Sales Report
                </h2>
                
                <!-- Date Range Form -->
                <form method="GET" class="needs-validation" novalidate>
                    <div class="row g-3 align-items-end">
                        <div class="col-md-5">
                            <label class="form-label small text-muted">Start Date</label>
                            <input type="date" name="start_date" class="form-control form-control-lg shadow-sm" 
                                   value="<?php echo $start_date; ?>" required>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label small text-muted">End Date</label>
                            <input type="date" name="end_date" class="form-control form-control-lg shadow-sm" 
                                   value="<?php echo $end_date; ?>" required>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary btn-lg w-100 mb-2 shadow-sm">
                                <i class="fa-solid fa-search me-2"></i>View Sales
                            </button>
                            <?php
                            $filename = 'monthly_sales_' . date('MY', strtotime($start_date));
                            ?>
                            <a href="generate_sales_pdf.php?start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>&filename=<?php echo $filename; ?>" 
                                class="btn btn-outline-danger btn-lg w-100 mb-2 shadow-sm"
                                target="_blank">
                                <i class="fa-solid fa-file-pdf me-2"></i>Export PDF
                            </a>
                            <a href="../pages/reports.php" class="btn btn-secondary btn-lg w-100 shadow-sm">
                                <i class="fa-solid fa-arrow-left me-2"></i>Back
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sales Records Table -->
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th class="text-end">Daily Sales</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sales_records as $record): ?>
                        <tr>
                            <td><?php echo date('M d, Y', strtotime($record['sale_date'])); ?></td>
                            <td class="text-end">₱<?php echo number_format($record['daily_total'], 2); ?></td>
                        </tr>
                    <?php endforeach; ?>
                    
                    <?php if (!empty($sales_records)): ?>
                        <tr class="table-primary fw-bold">
                            <td class="text-end">Total Sales:</td>
                            <td class="text-end">₱<?php echo number_format($total_sales, 2); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
