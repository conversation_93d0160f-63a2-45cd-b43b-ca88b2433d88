<?php
session_start();
require_once 'config/database.php';

echo "<h2>Payment Processing Debug Information</h2>";

// Check if we have a transaction ID to debug
$transaction_id = $_GET['transaction_id'] ?? null;

if (!$transaction_id) {
    echo "<p>Please provide a transaction_id parameter in the URL</p>";
    echo "<p>Example: debug_payment_issue.php?transaction_id=1</p>";
    exit;
}

echo "<h3>1. Database Schema Check</h3>";

// Check if new schema columns exist
$columns_to_check = ['total_amount', 'amount_paid', 'balance', 'status'];
$existing_columns = [];

foreach ($columns_to_check as $column) {
    $check = $conn->query("SHOW COLUMNS FROM transactions LIKE '$column'");
    if ($check->rowCount() > 0) {
        $existing_columns[] = $column;
        echo "<p>✅ Column '$column' exists</p>";
    } else {
        echo "<p>❌ Column '$column' missing</p>";
    }
}

echo "<h3>2. Transaction Data</h3>";

// Get transaction data
$sql = "SELECT * FROM transactions WHERE transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transaction_id]);
$transaction = $stmt->fetch(PDO::FETCH_ASSOC);

if ($transaction) {
    echo "<pre>";
    print_r($transaction);
    echo "</pre>";
} else {
    echo "<p>❌ Transaction not found</p>";
    exit;
}

echo "<h3>3. Transaction Details</h3>";

// Get transaction details
$sql = "SELECT * FROM transaction_details WHERE transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transaction_id]);
$details = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($details) {
    echo "<pre>";
    print_r($details);
    echo "</pre>";
    
    $total_items = array_sum(array_column($details, 'subtotal'));
    echo "<p><strong>Total from items: ₱" . number_format($total_items, 2) . "</strong></p>";
} else {
    echo "<p>⚠️ No transaction details found</p>";
}

echo "<h3>4. Addons</h3>";

// Get addons
$sql = "SELECT * FROM addons WHERE transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transaction_id]);
$addons = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($addons) {
    echo "<pre>";
    print_r($addons);
    echo "</pre>";
    
    $total_addons = array_sum(array_column($addons, 'trans_subtotal'));
    echo "<p><strong>Total from addons: ₱" . number_format($total_addons, 2) . "</strong></p>";
} else {
    echo "<p>ℹ️ No addons found</p>";
    $total_addons = 0;
}

echo "<h3>5. Payments</h3>";

// Get payments
$sql = "SELECT * FROM payments WHERE transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transaction_id]);
$payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($payments) {
    echo "<pre>";
    print_r($payments);
    echo "</pre>";
    
    $total_payments = array_sum(array_column($payments, 'amount_paid'));
    echo "<p><strong>Total payments: ₱" . number_format($total_payments, 2) . "</strong></p>";
} else {
    echo "<p>ℹ️ No payments found</p>";
    $total_payments = 0;
}

echo "<h3>6. Customer Info</h3>";

// Get customer info
$sql = "SELECT c.* FROM customers c 
        INNER JOIN transactions t ON c.customer_id = t.customer_id 
        WHERE t.transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transaction_id]);
$customer = $stmt->fetch(PDO::FETCH_ASSOC);

if ($customer) {
    echo "<pre>";
    print_r($customer);
    echo "</pre>";
} else {
    echo "<p>❌ Customer not found</p>";
}

echo "<h3>7. Loyalty Points</h3>";

if ($customer) {
    $sql = "SELECT * FROM loyalty_points WHERE customer_id = ? AND status = 'active'";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer['customer_id']]);
    $loyalty_points = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($loyalty_points) {
        echo "<pre>";
        print_r($loyalty_points);
        echo "</pre>";
        
        $available_points = array_sum(array_column($loyalty_points, 'points'));
        echo "<p><strong>Available points: " . $available_points . "</strong></p>";
    } else {
        echo "<p>ℹ️ No loyalty points found</p>";
    }
}

echo "<h3>8. Calculated Totals</h3>";

$grand_total = ($total_items ?? 0) + ($total_addons ?? 0);
$balance = $grand_total - ($total_payments ?? 0);

echo "<p><strong>Grand Total: ₱" . number_format($grand_total, 2) . "</strong></p>";
echo "<p><strong>Total Paid: ₱" . number_format($total_payments ?? 0, 2) . "</strong></p>";
echo "<p><strong>Balance: ₱" . number_format($balance, 2) . "</strong></p>";

if ($balance <= 0) {
    echo "<p>⚠️ <strong>Transaction appears to be fully paid!</strong></p>";
}

echo "<h3>9. Test Payment Validation</h3>";

// Test validation
require_once 'includes/validation.php';
$validator = new TransactionValidator($conn);

// Test business rules validation
$business_validation = $validator->validateBusinessRules($transaction_id, 'process_payment');
echo "<p><strong>Business Rules Validation:</strong></p>";
if ($business_validation['valid']) {
    echo "<p>✅ Business rules validation passed</p>";
} else {
    echo "<p>❌ Business rules validation failed:</p>";
    echo "<ul>";
    foreach ($business_validation['errors'] as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    echo "</ul>";
}

// Test payment validation with sample data
$sample_payment_data = [
    'payment_method' => 'cash',
    'amount_paid' => 100,
    'gcash_reference' => '',
    'points_used' => 0
];

$payment_validation = $validator->validatePayment($sample_payment_data, $customer['customer_id'] ?? 1, $balance);
echo "<p><strong>Payment Validation (sample cash payment of ₱100):</strong></p>";
if ($payment_validation['valid']) {
    echo "<p>✅ Payment validation passed</p>";
} else {
    echo "<p>❌ Payment validation failed:</p>";
    echo "<ul>";
    foreach ($payment_validation['errors'] as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    echo "</ul>";
}

echo "<h3>10. Recommendations</h3>";

if (empty($existing_columns)) {
    echo "<p>🔧 <strong>Action Required:</strong> Run the database schema improvements script to add missing columns.</p>";
    echo "<p>Execute: <code>database/schema_improvements.sql</code></p>";
}

if ($balance <= 0) {
    echo "<p>💡 <strong>Note:</strong> This transaction appears to be fully paid. You cannot add more payments to a fully paid transaction.</p>";
}

if (empty($details)) {
    echo "<p>⚠️ <strong>Warning:</strong> This transaction has no items. Add some laundry items before processing payment.</p>";
}

echo "<hr>";
echo "<p><a href='pages/transaction_details.php?transaction_id=$transaction_id'>← Back to Transaction Details</a></p>";
?>
