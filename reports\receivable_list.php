<?php
include_once '../config/database.php';

// Get receivables data
$sql = "SELECT p.transaction_id, 
    SUM(p.amount_paid) as total_amount_paid,
    MAX(p.transaction_amount) as transaction_amount,
    MAX(p.transaction_amount) - SUM(p.amount_paid) as remaining_balance,
    c.full_name
FROM payments p
JOIN transactions t ON p.transaction_id = t.transaction_id
JOIN customers c ON t.customer_id = c.customer_id
WHERE 1=1
AND NOT EXISTS (
    SELECT 1 FROM payments pm 
    WHERE pm.transaction_id = p.transaction_id 
    AND pm.method = 'loyalty'
    AND pm.amount_paid <= 0
)";

if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
    $sql .= " AND DATE(t.transaction_date) >= '" . $_GET['start_date'] . "'";
}

if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
    $sql .= " AND DATE(t.transaction_date) <= '" . $_GET['end_date'] . "'";
}

$sql .= " GROUP BY p.transaction_id, c.full_name
HAVING remaining_balance > 0
ORDER BY p.transaction_id";



$stmt = $conn->prepare($sql);
$stmt->execute();
$receivables = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate total receivables
$total_receivables = array_sum(array_column($receivables, 'remaining_balance'));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Receivables List</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <!-- Report Header Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
            <h2 class="card-title h4 mb-4">
                <i class="fa-solid fa-money-bill-wave me-2"></i>Receivables List Report
            </h2>
            
            <form method="GET" class="needs-validation" novalidate>
                <div class="row g-3 align-items-end">
                    <div class="col-md-5">
                        <label class="form-label small text-muted">Start Date</label>
                        <input type="date" 
                               name="start_date" 
                               class="form-control form-control-lg shadow-sm" 
                               value="<?php echo isset($_GET['start_date']) ? $_GET['start_date'] : ''; ?>" 
                               required>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label small text-muted">End Date</label>
                        <input type="date" 
                               name="end_date" 
                               class="form-control form-control-lg shadow-sm" 
                               value="<?php echo isset($_GET['end_date']) ? $_GET['end_date'] : ''; ?>" 
                               required>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary btn-lg w-100 mb-2 shadow-sm">
                            <i class="fa-solid fa-filter me-2"></i>Filter
                        </button>
                        <?php $filename = 'receivables_list_' . date('MY'); ?>
                        <a href="../reports/generate_receivables_pdf.php?start_date=<?php echo isset($_GET['start_date']) ? $_GET['start_date'] : ''; ?>&end_date=<?php echo isset($_GET['end_date']) ? $_GET['end_date'] : ''; ?>&filename=<?php echo $filename; ?>" 
                            class="btn btn-outline-danger btn-lg w-100 shadow-sm"
                            target="_blank">
                             <i class="fa-solid fa-file-pdf me-2"></i>Export PDF
                        </a>
                        <a href="../pages/reports.php" class="btn btn-secondary btn-lg w-100 shadow-sm mt-2">
                            <i class="fa-solid fa-arrow-left me-2"></i>Back
                        </a>
                    </div>
                </div>
            </form>
            </div>
        </div>

        <!-- Receivables Records Table -->
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Transaction ID</th>
                        <th>Customer Name</th>
                        <th>Total Amount</th>
                        <th>Amount Paid</th>
                        <th>Remaining Balance</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($receivables as $record): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($record['transaction_id']); ?></td>
                            <td><?php echo htmlspecialchars($record['full_name']); ?></td>
                            <td>₱<?php echo number_format($record['transaction_amount'], 2); ?></td>
                            <td>₱<?php echo number_format($record['total_amount_paid'], 2); ?></td>
                            <td>₱<?php echo number_format($record['remaining_balance'], 2); ?></td>
                            <td>
                                <a href="../pages/transaction_details.php?transaction_id=<?php echo $record['transaction_id']; ?>" class="btn btn-primary btn-sm">
                                    <i class="fa-solid fa-circle-info me-2"></i>Details
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    
                    <?php if (!empty($receivables)): ?>
                        <tr class="table-primary fw-bold">
                            <td colspan="5" class="text-end">Total Receivables:</td>
                            <td>₱<?php echo number_format($total_receivables, 2); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
