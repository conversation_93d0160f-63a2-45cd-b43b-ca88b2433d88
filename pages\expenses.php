<?php
session_start(); // Start the session to get employee_id
include_once '../config/database.php';

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $expense_date = $_POST['expense_date'];
        $description = $_POST['description'];
        $amount = $_POST['amount'];
        $recorded_by = $_SESSION['employee_id']; // Get from session
        
        $sql = "INSERT INTO expenses (expense_date, description, amount, recorded_by, created_at) 
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$expense_date, $description, $amount, $recorded_by]);
    }
}

if (isset($_POST['update']) && isset($_POST['expense_id'])) {
    $expense_id = $_POST['expense_id'];
    $expense_date = $_POST['expense_date'];
    $description = $_POST['description'];
    $amount = $_POST['amount'];
    
    $sql = "UPDATE expenses SET expense_date=?, description=?, amount=? WHERE expense_id=?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$expense_date, $description, $amount, $expense_id]);
}

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT e.*, emp.full_name as employee_name 
        FROM expenses e 
        LEFT JOIN employees emp ON e.recorded_by = emp.employee_id 
        WHERE description LIKE ? OR DATE(expense_date) LIKE ?";
$search_term = "%$search%";
$stmt = $conn->prepare($sql);
$stmt->execute([$search_term, $search_term]);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Expense Management</title>
     <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; }
        .table thead th { background-color: #0d6efd; color: white; }
        .input-group { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Expense Management</h2>
        
        <!-- Search Form -->
        <form method="GET" class="mb-4">
            <div class="input-group">
                <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                <input type="text" name="search" class="form-control" placeholder="Search expenses..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-primary">Search</button>
            </div>
        </form>

        <!-- Modified Add/Edit Expense Form -->
        <form method="POST" class="mb-4">
            <input type="hidden" name="expense_id" id="expense_id">
            <div class="row mb-3">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-calendar"></i></span>
                        <input type="date" name="expense_date" class="form-control" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-file-text"></i></span>
                        <input type="text" name="description" class="form-control" placeholder="Description" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-money-bill"></i></span>
                        <input type="number" step="0.01" name="amount" class="form-control" placeholder="Amount" required>
                    </div>
                </div>
                <div class="col">
                    <div class="btn-group" role="group">
                        <button type="submit" name="save" class="btn btn-success">Save New</button>
                        <button type="submit" name="update" class="btn btn-warning">Update</button>
                        <a href="../index.php" class="btn btn-secondary">Home</a>
                    </div>
                </div>
            </div>
        </form>

        <!-- Modified Expenses Table -->
        <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-striped">
                <thead style="position: sticky; top: 0; background: white;">
                    <tr>
                        <th>ID</th>
                        <th>Date</th>
                        <th>Description</th>
                        <th>Amount</th>
                        <th>Recorded By</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($result as $row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['expense_id']); ?></td>
                        <td><?php echo htmlspecialchars($row['expense_date']); ?></td>
                        <td><?php echo htmlspecialchars($row['description']); ?></td>
                        <td><?php echo htmlspecialchars(number_format($row['amount'], 2)); ?></td>
                        <td><?php echo htmlspecialchars($row['employee_name']); ?></td>
                        <td><?php echo htmlspecialchars($row['created_at']); ?></td>
                        <td>
                            <button onclick="editExpense(<?php echo htmlspecialchars(json_encode($row)); ?>)" 
                                    class="btn btn-sm btn-primary">Edit</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function editExpense(expense) {
        document.querySelector('[name="expense_id"]').value = expense.expense_id;
        document.querySelector('[name="expense_date"]').value = expense.expense_date;
        document.querySelector('[name="description"]').value = expense.description;
        document.querySelector('[name="amount"]').value = expense.amount;
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
