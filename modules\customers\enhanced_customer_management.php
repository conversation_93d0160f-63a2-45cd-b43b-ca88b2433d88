<?php
session_start();
require_once '../../config/database.php';

/**
 * Enhanced Customer Management System
 * Provides comprehensive customer relationship management capabilities
 */

class CustomerManager {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get comprehensive customer data with analytics
     */
    public function getCustomerWithAnalytics($customerId) {
        $sql = "SELECT 
                    c.*,
                    COUNT(t.transaction_id) as total_transactions,
                    COALESCE(SUM(t.total_amount), 0) as total_spent,
                    COALESCE(SUM(t.amount_paid), 0) as total_paid,
                    COALESCE(SUM(t.balance), 0) as outstanding_balance,
                    COALESCE(AVG(t.total_amount), 0) as avg_transaction_value,
                    MIN(t.transaction_date) as first_transaction,
                    MAX(t.transaction_date) as last_transaction,
                    DATEDIFF(CURDATE(), MAX(t.transaction_date)) as days_since_last_transaction,
                    COALESCE(lp.points, 0) as loyalty_points,
                    -- Customer segmentation
                    CASE 
                        WHEN COALESCE(SUM(t.total_amount), 0) >= 10000 THEN 'VIP'
                        WHEN COALESCE(SUM(t.total_amount), 0) >= 5000 THEN 'Premium'
                        WHEN COALESCE(SUM(t.total_amount), 0) >= 1000 THEN 'Regular'
                        ELSE 'New'
                    END as customer_segment,
                    -- Customer status
                    CASE 
                        WHEN DATEDIFF(CURDATE(), MAX(t.transaction_date)) <= 30 THEN 'Active'
                        WHEN DATEDIFF(CURDATE(), MAX(t.transaction_date)) <= 90 THEN 'At Risk'
                        WHEN MAX(t.transaction_date) IS NULL THEN 'New'
                        ELSE 'Inactive'
                    END as customer_status
                FROM customers c
                LEFT JOIN transactions t ON c.customer_id = t.customer_id AND t.status != 'CANCELLED'
                LEFT JOIN loyalty_points lp ON c.customer_id = lp.customer_id AND lp.status = 'active'
                WHERE c.customer_id = ?
                GROUP BY c.customer_id";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$customerId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get customer transaction history
     */
    public function getCustomerTransactionHistory($customerId, $limit = 20) {
        $sql = "SELECT 
                    t.transaction_id,
                    t.transaction_date,
                    t.total_amount,
                    t.amount_paid,
                    t.balance,
                    t.status,
                    t.remarks,
                    GROUP_CONCAT(DISTINCT td.category SEPARATOR ', ') as services,
                    SUM(td.weight) as total_weight
                FROM transactions t
                LEFT JOIN transaction_details td ON t.transaction_id = td.transaction_id
                WHERE t.customer_id = ?
                GROUP BY t.transaction_id, t.transaction_date, t.total_amount, t.amount_paid, t.balance, t.status, t.remarks
                ORDER BY t.transaction_date DESC
                LIMIT ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$customerId, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get customer service preferences
     */
    public function getCustomerServicePreferences($customerId) {
        $sql = "SELECT 
                    td.category,
                    COUNT(*) as usage_count,
                    SUM(td.weight) as total_weight,
                    AVG(td.weight) as avg_weight,
                    SUM(td.subtotal) as total_spent,
                    AVG(td.subtotal) as avg_spent,
                    MAX(t.transaction_date) as last_used
                FROM transaction_details td
                INNER JOIN transactions t ON td.transaction_id = t.transaction_id
                WHERE t.customer_id = ? AND t.status != 'CANCELLED'
                GROUP BY td.category
                ORDER BY usage_count DESC, total_spent DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$customerId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Search customers with advanced filters
     */
    public function searchCustomers($searchTerm = '', $segment = '', $status = '', $limit = 50, $offset = 0) {
        $conditions = [];
        $params = [];
        
        if (!empty($searchTerm)) {
            $conditions[] = "(c.full_name LIKE ? OR c.phone_number LIKE ? OR c.address LIKE ?)";
            $searchParam = "%$searchTerm%";
            $params[] = $searchParam;
            $params[] = $searchParam;
            $params[] = $searchParam;
        }
        
        $havingConditions = [];
        if (!empty($segment)) {
            $havingConditions[] = "customer_segment = ?";
            $params[] = $segment;
        }
        
        if (!empty($status)) {
            $havingConditions[] = "customer_status = ?";
            $params[] = $status;
        }
        
        $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
        $havingClause = !empty($havingConditions) ? 'HAVING ' . implode(' AND ', $havingConditions) : '';
        
        $sql = "SELECT 
                    c.customer_id,
                    c.full_name,
                    c.phone_number,
                    c.address,
                    c.created_at,
                    COUNT(t.transaction_id) as total_transactions,
                    COALESCE(SUM(t.total_amount), 0) as total_spent,
                    COALESCE(SUM(t.balance), 0) as outstanding_balance,
                    MAX(t.transaction_date) as last_transaction,
                    COALESCE(lp.points, 0) as loyalty_points,
                    CASE 
                        WHEN COALESCE(SUM(t.total_amount), 0) >= 10000 THEN 'VIP'
                        WHEN COALESCE(SUM(t.total_amount), 0) >= 5000 THEN 'Premium'
                        WHEN COALESCE(SUM(t.total_amount), 0) >= 1000 THEN 'Regular'
                        ELSE 'New'
                    END as customer_segment,
                    CASE 
                        WHEN DATEDIFF(CURDATE(), MAX(t.transaction_date)) <= 30 THEN 'Active'
                        WHEN DATEDIFF(CURDATE(), MAX(t.transaction_date)) <= 90 THEN 'At Risk'
                        WHEN MAX(t.transaction_date) IS NULL THEN 'New'
                        ELSE 'Inactive'
                    END as customer_status
                FROM customers c
                LEFT JOIN transactions t ON c.customer_id = t.customer_id AND t.status != 'CANCELLED'
                LEFT JOIN loyalty_points lp ON c.customer_id = lp.customer_id AND lp.status = 'active'
                $whereClause
                GROUP BY c.customer_id, c.full_name, c.phone_number, c.address, c.created_at, lp.points
                $havingClause
                ORDER BY total_spent DESC, last_transaction DESC
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get customer statistics
     */
    public function getCustomerStatistics() {
        $sql = "SELECT 
                    COUNT(*) as total_customers,
                    COUNT(CASE WHEN customer_segment = 'VIP' THEN 1 END) as vip_customers,
                    COUNT(CASE WHEN customer_segment = 'Premium' THEN 1 END) as premium_customers,
                    COUNT(CASE WHEN customer_segment = 'Regular' THEN 1 END) as regular_customers,
                    COUNT(CASE WHEN customer_segment = 'New' THEN 1 END) as new_customers,
                    COUNT(CASE WHEN customer_status = 'Active' THEN 1 END) as active_customers,
                    COUNT(CASE WHEN customer_status = 'At Risk' THEN 1 END) as at_risk_customers,
                    COUNT(CASE WHEN customer_status = 'Inactive' THEN 1 END) as inactive_customers,
                    AVG(total_spent) as avg_customer_value,
                    SUM(outstanding_balance) as total_outstanding
                FROM (
                    SELECT 
                        c.customer_id,
                        COALESCE(SUM(t.total_amount), 0) as total_spent,
                        COALESCE(SUM(t.balance), 0) as outstanding_balance,
                        CASE 
                            WHEN COALESCE(SUM(t.total_amount), 0) >= 10000 THEN 'VIP'
                            WHEN COALESCE(SUM(t.total_amount), 0) >= 5000 THEN 'Premium'
                            WHEN COALESCE(SUM(t.total_amount), 0) >= 1000 THEN 'Regular'
                            ELSE 'New'
                        END as customer_segment,
                        CASE 
                            WHEN DATEDIFF(CURDATE(), MAX(t.transaction_date)) <= 30 THEN 'Active'
                            WHEN DATEDIFF(CURDATE(), MAX(t.transaction_date)) <= 90 THEN 'At Risk'
                            WHEN MAX(t.transaction_date) IS NULL THEN 'New'
                            ELSE 'Inactive'
                        END as customer_status
                    FROM customers c
                    LEFT JOIN transactions t ON c.customer_id = t.customer_id AND t.status != 'CANCELLED'
                    GROUP BY c.customer_id
                ) customer_analytics";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Add or update customer
     */
    public function saveCustomer($customerData, $customerId = null) {
        if ($customerId) {
            // Update existing customer
            $sql = "UPDATE customers 
                    SET full_name = ?, phone_number = ?, address = ?, updated_at = NOW()
                    WHERE customer_id = ?";
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([
                $customerData['full_name'],
                $customerData['phone_number'],
                $customerData['address'],
                $customerId
            ]);
        } else {
            // Add new customer
            $sql = "INSERT INTO customers (full_name, phone_number, address, created_at) 
                    VALUES (?, ?, ?, NOW())";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $customerData['full_name'],
                $customerData['phone_number'],
                $customerData['address']
            ]);
            
            if ($result) {
                return $this->conn->lastInsertId();
            }
            return false;
        }
    }
    
    /**
     * Delete customer (soft delete by marking as inactive)
     */
    public function deleteCustomer($customerId) {
        // Check if customer has transactions
        $checkSql = "SELECT COUNT(*) as transaction_count FROM transactions WHERE customer_id = ?";
        $checkStmt = $this->conn->prepare($checkSql);
        $checkStmt->execute([$customerId]);
        $result = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['transaction_count'] > 0) {
            // Customer has transactions, cannot delete
            return ['success' => false, 'message' => 'Cannot delete customer with existing transactions'];
        }
        
        // Safe to delete
        $sql = "DELETE FROM customers WHERE customer_id = ?";
        $stmt = $this->conn->prepare($sql);
        $success = $stmt->execute([$customerId]);
        
        return ['success' => $success, 'message' => $success ? 'Customer deleted successfully' : 'Failed to delete customer'];
    }
    
    /**
     * Get customers due for follow-up
     */
    public function getCustomersForFollowUp($daysSinceLastTransaction = 30) {
        $sql = "SELECT 
                    c.customer_id,
                    c.full_name,
                    c.phone_number,
                    MAX(t.transaction_date) as last_transaction,
                    DATEDIFF(CURDATE(), MAX(t.transaction_date)) as days_since_last_transaction,
                    COUNT(t.transaction_id) as total_transactions,
                    SUM(t.total_amount) as total_spent
                FROM customers c
                INNER JOIN transactions t ON c.customer_id = t.customer_id
                WHERE t.status != 'CANCELLED'
                GROUP BY c.customer_id, c.full_name, c.phone_number
                HAVING days_since_last_transaction >= ?
                ORDER BY total_spent DESC, days_since_last_transaction DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$daysSinceLastTransaction]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
