<?php
session_start();
include_once '../config/database.php'; 

// Get all employees for the dropdown
$sql = "SELECT employee_id, full_name FROM employees";
$stmt = $conn->prepare($sql);
$stmt->execute();
$employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get attendance records for selected employee and date range
$selected_employee = isset($_GET['employee_id']) ? $_GET['employee_id'] : '';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

$attendance_records = [];
if ($selected_employee && $start_date && $end_date) {
    $sql = "SELECT DATE(date) as attendance_date,
            MAX(CASE WHEN status = 'IN' THEN time END) as time_in,
            MAX(CASE WHEN status = 'OUT' THEN time END) as time_out
            FROM attendance 
            WHERE employee_id = ? 
            AND DATE(date) BETWEEN ? AND ?
            GROUP BY DATE(date)
            ORDER BY DATE(date)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$selected_employee, $start_date, $end_date]);
    $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Employee DTR</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="card shadow-sm mb-4">
            <div class="card-body">
            <h2 class="card-title h4 mb-4">
                <i class="fa-solid fa-clock me-2"></i>Employee Daily Time Record
            </h2>
            
            <!-- Employee Selection and Date Range Form -->
            <form method="GET" class="needs-validation" novalidate>
                <div class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label class="form-label small text-muted">Select Employee</label>
                    <select name="employee_id" class="form-select form-select-lg shadow-sm" required>
                    <option value="">Choose...</option>
                    <?php foreach ($employees as $employee): ?>
                    <option value="<?php echo $employee['employee_id']; ?>" 
                        <?php echo ($selected_employee == $employee['employee_id']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($employee['full_name']); ?>
                    </option>
                    <?php endforeach; ?>
                    <?php $_SESSION['employee_id'] = $employee['employee_id']; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small text-muted">Start Date</label>
                    <input type="date" name="start_date" class="form-control form-control-lg shadow-sm" 
                       value="<?php echo $start_date; ?>" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label small text-muted">End Date</label>
                    <input type="date" name="end_date" class="form-control form-control-lg shadow-sm" 
                       value="<?php echo $end_date; ?>" required>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary btn-lg w-100 mb-2 shadow-sm">
                    <i class="fa-solid fa-search me-2"></i>View DTR
                    </button>
                    <?php
                    // Get employee name for the selected employee
                    if ($selected_employee) {
                        $stmt = $conn->prepare("SELECT full_name FROM employees WHERE employee_id = ?");
                        $stmt->execute([$selected_employee]);
                        $emp_name = $stmt->fetch(PDO::FETCH_COLUMN);
                        // Format the filename: employeename_monthyear.pdf
                        $filename = str_replace(' ', '_', strtolower($emp_name)) . '_' . date('MY', strtotime($start_date));
                    } else {
                        $filename = 'dtr';
                    }
                    ?>
                    <a href="generate_dtr_pdf.php?employee_id=<?php echo $selected_employee; ?>&start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>&filename=<?php echo $filename; ?>" 
                       class="btn btn-outline-danger btn-lg w-100 shadow-sm"
                       target="_blank">
                        <i class="fa-solid fa-file-pdf me-2"></i>Export PDF
                    </a>
                    <a href="../pages/reports.php" class="btn btn-secondary btn-lg w-100 shadow-sm mt-2">
                        <i class="fa-solid fa-arrow-left me-2"></i>Back
                    </a>
                </div>
                </div>
            </form>
            </div>
        </div>

        <!-- Attendance Records Table -->
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Time In</th>
                        <th>Time Out</th>
                        <th>Total Hours</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($attendance_records as $record): ?>
                    <tr>
                        <td><?php echo date('M d, Y', strtotime($record['attendance_date'])); ?></td>
                        <td><?php echo $record['time_in'] ? date('h:i A', strtotime($record['time_in'])) : '-'; ?></td>
                        <td><?php echo $record['time_out'] ? date('h:i A', strtotime($record['time_out'])) : '-'; ?></td>
                        <td>
                            <?php
                            if ($record['time_in'] && $record['time_out']) {
                                $time_in = strtotime($record['time_in']);
                                $time_out = strtotime($record['time_out']);
                                $diff = round(($time_out - $time_in) / 3600, 2);
                                echo $diff . ' hrs';
                            } else {
                                echo '-';
                            }
                            ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
