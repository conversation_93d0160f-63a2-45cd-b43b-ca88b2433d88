<?php
session_start();
include_once './config/database.php';


// Fetch all employees for the dropdown
$sql = "SELECT * FROM profile";
$stmt = $conn->prepare($sql);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single row

// Ensure business_name is retrieved correctly
$business_name = $profile['business_name'] ?? "Super Hero Laundry Squad"; 
$business_owner = $profile['business_owner'] ??""; 
$business_address = $profile['business_address'] ??""; 
$business_cell = $profile['business_cell'] ??""; 
$business_land = $profile['business_land'] ??""; 
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Hero Laundry Squad - Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
    /* ...existing code... */
body {
    display: grid;
    grid-template-rows: 1fr 10rem auto;
    grid-template-areas: "main" "." "footer";
    overflow-x: hidden;
    background: #F5F7FA;
    min-height: 100vh;
    font-family: 'Open Sans', sans-serif;
}
.footer {
    z-index: 1;
    --footer-background:rgb(48, 123, 235);
    display: grid;
    position: relative;
    grid-area: footer;
    min-height: 12rem;
}
.footer .bubbles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1rem;
    background: var(--footer-background);
    filter: url("#blob");
}
.footer .bubble {
    position: absolute;
    left: var(--position, 50%);
    background: var(--footer-background);
    border-radius: 100%;
    animation: bubble-size var(--time, 4s) ease-in infinite var(--delay, 0s),
        bubble-move var(--time, 4s) ease-in infinite var(--delay, 0s);
    transform: translate(-50%, 100%);
}
.footer .content {
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr auto;
    grid-gap: 4rem;
    padding: 2rem;
    background: var(--footer-background);
}
.footer .content a,
.footer .content p {
    color: #F5F7FA;
    text-decoration: none;
}
.footer .content b {
    color: white;
}
.footer .content p {
    margin: 0;
    font-size: .75rem;
}
.footer .content > div {
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.footer .content > div > div {
    margin: 0.25rem 0;
}
.footer .content > div > div > * {
    margin-right: .5rem;
}
.footer .content .image {
    align-self: center;
    width: 4rem;
    height: 4rem;
    margin: 0.25rem 0;
    background-size: cover;
    background-position: center;
}
@keyframes bubble-size {
    0%, 75% {
        width: var(--size, 4rem);
        height: var(--size, 4rem);
    }
    100% {
        width: 0rem;
        height: 0rem;
    }
}
@keyframes bubble-move {
    0% {
        bottom: -4rem;
    }
    100% {
        bottom: var(--distance, 10rem);
    }
}
    </style>
</head>
<body>
    <div class="main" style="grid-area:main;display:flex;align-items:center;justify-content:center;min-height:80vh;">
        <div class="login-container">
            <h1 class="text-center mb-4">💪 <?php echo htmlspecialchars($business_name, ENT_QUOTES); ?>! 🦸‍♂️</h1>
            <p class="text-center mb-4">
                <strong>Owner:</strong> <?php echo htmlspecialchars($business_owner, ENT_QUOTES); ?><br>
                <strong>Address:</strong> <?php echo htmlspecialchars($business_address, ENT_QUOTES); ?><br>
                <strong>Cell:</strong> <?php echo htmlspecialchars($business_cell, ENT_QUOTES); ?><br>
                <strong>Landline:</strong> <?php echo htmlspecialchars($business_land, ENT_QUOTES); ?>
            </p>
            
            <form action="process_login.php" method="POST">
                <div class="mb-3">
                    <label for="username" class="form-label">🦹‍♂️ Codename</label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">🛡️ Secret Code</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="password" name="password" required>
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                            👁️
                        </button>
                    </div>
                </div>
                <script>
                function togglePassword() {
                    const pwd = document.getElementById("password");
                    pwd.type = pwd.type === "password" ? "text" : "password";
                }
                </script>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">ACTIVATE MISSION! 🚀</button>
                </div>
                <?php if(isset($_SESSION['username']) && isset($_SESSION['password'])): ?>
                    <div class="text-center mt-3">
                        <a href="./includes/change_password.php?username=<?php echo htmlspecialchars($_SESSION['username'], ENT_QUOTES); ?>&password=<?php echo htmlspecialchars($_SESSION['password'], ENT_QUOTES); ?>">Change Password 🔐</a>
                    </div>
                <?php endif; ?>
                <?php
                if(isset($_SESSION['error'])) {
                    echo '<div class="alert alert-danger mt-3">' . $_SESSION['error'] . '</div>';
                    unset($_SESSION['error']);
                }
                ?>
            </form>
        </div>
    </div>
    <div class="footer">
        <div class="bubbles"></div>
        <div class="content">
            <div style="display:flex;gap:2rem;">
                <div>
                    <div>🦸‍♂️ Super Hero Laundry Squad</div>
                    <div>🧼 Clean Clothes, Clean Conscience</div>
                </div>
                <div>
                    <a href="
            </div>
            <div>
                <a class="image" href="https://codepen.io/z-" target="_blank" style="background-image: url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/199011/happy.svg')"></a>
                <p>©2025 Eduardo Sabangan 0939 473 6830</p>
            </div>
        </div>
    </div>
    <svg style="position:fixed; top:100vh">
        <defs>
            <filter id="blob">
                <feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur"/>
                <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 19 -9" result="blob"/>
            </filter>
        </defs>
    </svg>
    <script>
        // Generate bubbles
        const bubbles = document.querySelector('.bubbles');
        for (let i = 0; i < 120; i++) { // You can use 128 for more bubbles
            const bubble = document.createElement('div');
            bubble.className = 'bubble';
            bubble.style.setProperty('--size', (2 + Math.random() * 8) + 'rem');
            bubble.style.setProperty('--distance', (6 + Math.random() * 4) + 'rem');
            bubble.style.setProperty('--position', (-5 + Math.random() * 110) + '%');
            bubble.style.setProperty('--time', (2 + Math.random() * 2) + 's');
            bubble.style.setProperty('--delay', (-1 * (2 + Math.random() * 2)) + 's');
            bubbles.appendChild(bubble);
        }
    </script>
</body>
</html>