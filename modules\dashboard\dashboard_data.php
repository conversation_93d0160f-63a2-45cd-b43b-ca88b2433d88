<?php
/**
 * Dashboard Data Provider
 * Provides comprehensive analytics data for the enhanced dashboard
 */

class DashboardDataProvider {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get Key Performance Indicators (KPIs)
     */
    public function getKPIs($period = 'today') {
        $dateCondition = $this->getDateCondition($period);
        
        $sql = "SELECT 
                    COUNT(DISTINCT t.transaction_id) as total_transactions,
                    COUNT(DISTINCT t.customer_id) as unique_customers,
                    COALESCE(SUM(t.total_amount), 0) as total_revenue,
                    COALESCE(SUM(t.amount_paid), 0) as total_collected,
                    COALESCE(SUM(t.balance), 0) as outstanding_balance,
                    COALESCE(AVG(t.total_amount), 0) as avg_transaction_value
                FROM transactions t 
                WHERE t.status != 'CANCELLED' AND $dateCondition";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        $kpis = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get expenses for the same period
        $expenseSql = "SELECT COALESCE(SUM(amount), 0) as total_expenses 
                       FROM expenses 
                       WHERE $dateCondition";
        $expenseStmt = $this->conn->prepare($expenseSql);
        $expenseStmt->execute();
        $expenses = $expenseStmt->fetch(PDO::FETCH_ASSOC);
        
        $kpis['total_expenses'] = $expenses['total_expenses'];
        $kpis['net_profit'] = $kpis['total_collected'] - $kpis['total_expenses'];
        
        return $kpis;
    }
    
    /**
     * Get sales trend data for charts
     */
    public function getSalesTrend($days = 30) {
        $sql = "SELECT 
                    DATE(t.transaction_date) as date,
                    COUNT(t.transaction_id) as transaction_count,
                    COALESCE(SUM(t.total_amount), 0) as daily_sales,
                    COALESCE(SUM(t.amount_paid), 0) as daily_collected
                FROM transactions t 
                WHERE t.status != 'CANCELLED' 
                AND t.transaction_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                GROUP BY DATE(t.transaction_date)
                ORDER BY DATE(t.transaction_date)";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$days]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get expense trend data
     */
    public function getExpenseTrend($days = 30) {
        $sql = "SELECT 
                    DATE(expense_date) as date,
                    COUNT(*) as expense_count,
                    COALESCE(SUM(amount), 0) as daily_expenses
                FROM expenses 
                WHERE expense_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                GROUP BY DATE(expense_date)
                ORDER BY DATE(expense_date)";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$days]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get top customers by revenue
     */
    public function getTopCustomers($limit = 10) {
        $sql = "SELECT 
                    c.customer_id,
                    c.full_name,
                    c.phone_number,
                    COUNT(t.transaction_id) as transaction_count,
                    COALESCE(SUM(t.total_amount), 0) as total_spent,
                    COALESCE(SUM(t.balance), 0) as outstanding_balance,
                    MAX(t.transaction_date) as last_transaction
                FROM customers c
                INNER JOIN transactions t ON c.customer_id = t.customer_id
                WHERE t.status != 'CANCELLED'
                GROUP BY c.customer_id, c.full_name, c.phone_number
                ORDER BY total_spent DESC
                LIMIT ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get service category performance
     */
    public function getServicePerformance() {
        $sql = "SELECT 
                    td.category,
                    COUNT(*) as service_count,
                    COALESCE(SUM(td.subtotal), 0) as total_revenue,
                    COALESCE(AVG(td.subtotal), 0) as avg_revenue,
                    COALESCE(SUM(td.weight), 0) as total_weight
                FROM transaction_details td
                INNER JOIN transactions t ON td.transaction_id = t.transaction_id
                WHERE t.status != 'CANCELLED'
                AND t.transaction_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY td.category
                ORDER BY total_revenue DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get payment method distribution
     */
    public function getPaymentMethodStats() {
        $sql = "SELECT 
                    p.method,
                    COUNT(*) as payment_count,
                    COALESCE(SUM(p.amount_paid), 0) as total_amount
                FROM payments p
                INNER JOIN transactions t ON p.transaction_id = t.transaction_id
                WHERE t.transaction_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY p.method
                ORDER BY total_amount DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get recent transactions
     */
    public function getRecentTransactions($limit = 10) {
        $sql = "SELECT 
                    t.transaction_id,
                    c.full_name as customer_name,
                    t.transaction_date,
                    t.total_amount,
                    t.amount_paid,
                    t.balance,
                    t.status
                FROM transactions t
                INNER JOIN customers c ON t.customer_id = c.customer_id
                ORDER BY t.transaction_date DESC
                LIMIT ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get employee attendance summary
     */
    public function getEmployeeAttendance() {
        $sql = "SELECT 
                    e.employee_id,
                    e.full_name,
                    e.position,
                    COUNT(CASE WHEN das.time_in IS NOT NULL THEN 1 END) as days_present,
                    COUNT(CASE WHEN das.time_in IS NULL THEN 1 END) as days_absent,
                    COALESCE(AVG(das.total_hours), 0) as avg_hours_per_day,
                    COALESCE(SUM(das.overtime_hours), 0) as total_overtime
                FROM employees e
                LEFT JOIN daily_attendance_summary das ON e.employee_id = das.employee_id
                    AND das.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY e.employee_id, e.full_name, e.position
                ORDER BY e.full_name";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get low stock alerts
     */
    public function getLowStockAlerts($threshold = 10) {
        $sql = "SELECT 
                    product_id,
                    name,
                    stock_quantity,
                    price
                FROM products 
                WHERE stock_quantity <= ?
                ORDER BY stock_quantity ASC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$threshold]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get outstanding receivables
     */
    public function getOutstandingReceivables() {
        $sql = "SELECT 
                    c.full_name as customer_name,
                    c.phone_number,
                    t.transaction_id,
                    t.transaction_date,
                    t.total_amount,
                    t.amount_paid,
                    t.balance,
                    DATEDIFF(CURDATE(), t.transaction_date) as days_overdue
                FROM transactions t
                INNER JOIN customers c ON t.customer_id = c.customer_id
                WHERE t.balance > 0
                ORDER BY t.transaction_date ASC
                LIMIT 20";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Helper method to generate date conditions
     */
    private function getDateCondition($period) {
        switch($period) {
            case 'today':
                return "DATE(COALESCE(transaction_date, expense_date)) = CURDATE()";
            case 'yesterday':
                return "DATE(COALESCE(transaction_date, expense_date)) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
            case 'this_week':
                return "YEARWEEK(COALESCE(transaction_date, expense_date)) = YEARWEEK(CURDATE())";
            case 'last_week':
                return "YEARWEEK(COALESCE(transaction_date, expense_date)) = YEARWEEK(CURDATE()) - 1";
            case 'this_month':
                return "YEAR(COALESCE(transaction_date, expense_date)) = YEAR(CURDATE()) AND MONTH(COALESCE(transaction_date, expense_date)) = MONTH(CURDATE())";
            case 'last_month':
                return "YEAR(COALESCE(transaction_date, expense_date)) = YEAR(CURDATE()) AND MONTH(COALESCE(transaction_date, expense_date)) = MONTH(CURDATE()) - 1";
            case 'this_year':
                return "YEAR(COALESCE(transaction_date, expense_date)) = YEAR(CURDATE())";
            default:
                return "DATE(COALESCE(transaction_date, expense_date)) = CURDATE()";
        }
    }
    
    /**
     * Get comparison data for period-over-period analysis
     */
    public function getComparisonData($currentPeriod = 'this_month', $previousPeriod = 'last_month') {
        $current = $this->getKPIs($currentPeriod);
        $previous = $this->getKPIs($previousPeriod);
        
        $comparison = [];
        foreach($current as $key => $value) {
            $prevValue = $previous[$key] ?? 0;
            $comparison[$key] = [
                'current' => $value,
                'previous' => $prevValue,
                'change' => $prevValue > 0 ? (($value - $prevValue) / $prevValue) * 100 : 0,
                'trend' => $value > $prevValue ? 'up' : ($value < $prevValue ? 'down' : 'stable')
            ];
        }
        
        return $comparison;
    }
}
?>
