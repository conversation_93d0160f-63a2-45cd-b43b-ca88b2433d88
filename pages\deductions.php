<?php
// Database connection parameters
include_once '../config/database.php'; 

// Get current month and year
$current_month = date('m');
$current_year = date('Y');

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['save'])) {
        $employee_id = $_POST['employee_id'];
        // Use the selected month and year from the GET parameters since they contain the current selection
        $deduct_month = isset($_GET['month']) ? $_GET['month'] : $current_month;
        $deduct_year = isset($_GET['year']) ? $_GET['year'] : $current_year;
        $deduct_des = $_POST['deduct_des'];
        $deduct_fees = $_POST['deduct_fees'];
        
        $sql = "INSERT INTO deductions (employee_id, deduct_month, deduct_year, deduct_des, deduct_fees) 
                VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$employee_id, intval($deduct_month), intval($deduct_year), $deduct_des, $deduct_fees]);
    }

    if (isset($_POST['update']) && isset($_POST['deduction_id'])) {
        $deduction_id = $_POST['deduction_id'];
        $deduct_des = $_POST['deduct_des'];
        $deduct_fees = $_POST['deduct_fees'];
        
        $sql = "UPDATE deductions SET deduct_des=?, deduct_fees=? WHERE deduct_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$deduct_des, $deduct_fees, $deduction_id]);
    }

    if (isset($_POST['delete']) && isset($_POST['deduction_id'])) {
        $deduction_id = $_POST['deduction_id'];
        
        $sql = "DELETE FROM deductions WHERE deduct_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$deduction_id]);
    }
}

// Get employees list
$sql = "SELECT employee_id, full_name FROM employees";
$stmt = $conn->prepare($sql);
$stmt->execute();
$employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$month = isset($_GET['month']) ? $_GET['month'] : (isset($_POST['month']) ? $_POST['month'] : $current_month);
$year = isset($_GET['year']) ? $_GET['year'] : (isset($_POST['year']) ? $_POST['year'] : $current_year);

$sql = "SELECT d.*, e.full_name 
        FROM deductions d 
        JOIN employees e ON d.employee_id = e.employee_id 
        WHERE (e.full_name LIKE ? OR d.deduct_des LIKE ?) 
        AND d.deduct_month = ? 
        AND d.deduct_year = ?";
$search_term = "%$search%";
$stmt = $conn->prepare($sql);
$stmt->execute([$search_term, $search_term, $month, $year]);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Deductions Management</title>
    <link rel="icon" type="image/x-icon" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1200px; }
        .table thead th { background-color: #0d6efd; color: white; }
        .input-group { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Deductions Management</h2>
        
        <!-- Search Form -->
        <form method="GET" class="mb-4">
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                        <input type="text" name="search" class="form-control" placeholder="Search deductions..." value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                </div>
                <div class="col-md-2">
                    <select name="month" class="form-control">
                        <?php 
                        $selected_month = isset($_GET['month']) ? $_GET['month'] : date('m');
                        for($m=1; $m<=12; $m++): ?>
                            <option value="<?php echo $m; ?>" <?php echo $m == $selected_month ? 'selected' : ''; ?>>
                                <?php echo date('F', mktime(0,0,0,$m,1)); ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="year" class="form-control">
                        <?php 
                        $selected_year = isset($_GET['year']) ? $_GET['year'] : date('Y');
                        for($y=date('Y')-2; $y<=date('Y')+2; $y++): ?>
                            <option value="<?php echo $y; ?>" <?php echo $y == $selected_year ? 'selected' : ''; ?>>
                                <?php echo $y; ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100"><i class="fa-solid fa-search"></i> Search</button>
                </div>
            </div>
        </form>

        <!-- Add/Edit Deduction Form -->
        <form method="POST" class="mb-4">
            <input type="hidden" name="deduction_id" id="deduction_id">
            <div class="row mb-3">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-user"></i></span>
                        <select name="employee_id" class="form-control" required>
                            <option value="">Select Employee</option>
                            <?php foreach($employees as $employee): ?>
                                <option value="<?php echo $employee['employee_id']; ?>">
                                    <?php echo htmlspecialchars($employee['full_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-file-text"></i></span>
                        <input type="text" name="deduct_des" class="form-control" placeholder="Deduction Description" required>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa-solid fa-money-bill"></i></span>
                        <input type="number" step="0.01" name="deduct_fees" class="form-control" placeholder="Amount" required>
                    </div>
                </div>
                <div class="col">
                    <div class="btn-group" role="group">
                        <button type="submit" name="save" class="btn btn-success"><i class="fa-solid fa-plus"></i> Save New</button>
                        <button type="submit" name="update" class="btn btn-warning"><i class="fa-solid fa-pen"></i> Update</button>
                        <a href="../reports/payroll_section.php" class="btn btn-secondary"><i class="fa-solid fa-home"></i> Payroll Report Home</a>
                    </div>
                </div>
            </div>
        </form>

        <!-- Deductions Table -->
        <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-striped">
                <thead style="position: sticky; top: 0; background: white;">
                    <tr>
                        <th><i class="fa-solid fa-id-card"></i> ID</th>
                        <th><i class="fa-solid fa-user"></i> Employee</th>
                        <th><i class="fa-solid fa-calendar"></i> Month/Year</th>
                        <th><i class="fa-solid fa-file-text"></i> Description</th>
                        <th><i class="fa-solid fa-money-bill"></i> Amount</th>
                        <th><i class="fa-solid fa-gear"></i> Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($result as $row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['deduct_id']); ?></td>
                        <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                        <td><?php echo date('F', mktime(0,0,0,$row['deduct_month'],1)) . ' ' . $row['deduct_year']; ?></td>
                        <td><?php echo htmlspecialchars($row['deduct_des']); ?></td>
                        <td><?php echo htmlspecialchars($row['deduct_fees']); ?></td>
                        <td>
                            <button onclick="editDeduction(<?php echo htmlspecialchars(json_encode($row)); ?>)" 
                                    class="btn btn-sm btn-primary">Edit</button>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="deduction_id" value="<?php echo $row['deduct_id']; ?>">
                                <button type="submit" name="delete" class="btn btn-sm btn-danger" 
                                        onclick="return confirm('Are you sure you want to delete this deduction?')">Delete</button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function editDeduction(deduction) {
        document.querySelector('[name="deduction_id"]').value = deduction.id;
        document.querySelector('[name="employee_id"]').value = deduction.employee_id;
        document.querySelector('[name="deduct_des"]').value = deduction.deduct_des;
        document.querySelector('[name="deduct_fees"]').value = deduction.deduct_fees;
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
