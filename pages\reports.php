<?php
include_once '../config/database.php';

function getCurrentMonthRange() {
    $start = date('Y-m-01');
    $end = date('Y-m-t');
    return ['start' => $start, 'end' => $end];
}

$report_type = isset($_GET['report']) ? $_GET['report'] : '';
$date_range = getCurrentMonthRange();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-bg: #f8f9fa;
            --card-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
        }
        body {
            background-color: var(--primary-bg);
            font-family: 'Inter', sans-serif;
        }
        .report-card {
            border: none;
            border-radius: 15px;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 30px 0px;
        }
        .icon-large {
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
        }
        .btn-custom {
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: scale(1.05);
        }
        .dashboard-header {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
        }
        .card-body {
            padding: 2rem;
        }
        .card-title {
            font-weight: 600;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="dashboard-header">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0 fw-bold">Reports Dashboard</h2>
                <a href="../index.php" class="btn btn-custom btn-light">
                    <i class="fas fa-home me-2"></i>Home
                </a>
            </div>
        </div>

        <div class="row g-4">
            <?php
            $reports = [
                [
                    'title' => 'Employee Daily Time Record',
                    'icon' => 'clock',
                    'color' => 'primary',
                    'url' => '../reports/employee_dtr.php'
                ],
                [
                    'title' => 'Monthly Expenses',
                    'icon' => 'money-bill-wave',
                    'color' => 'danger',
                    'url' => '../reports/monthly_expenses.php'
                ],
                [
                    'title' => 'Monthly Sales',
                    'icon' => 'chart-line',
                    'color' => 'success',
                    'url' => '../reports/monthly_sales.php'
                ],
                [
                    'title' => 'Monthly Receivables',
                    'icon' => 'file-invoice-dollar',
                    'color' => 'warning',
                    'url' => '../reports/monthly_receivables.php'
                ],
                [
                    'title' => 'Customers List',
                    'icon' => 'users',
                    'color' => 'info',
                    'url' => 'reports/customers_list.php'
                ],
                [
                    'title' => 'Monthly Profit Report',
                    'icon' => 'coins',
                    'color' => 'success',
                    'url' => '../reports/monthly_profit.php'
                ],
                [
                    'title' => 'GCash Payments',
                    'icon' => 'mobile-alt',
                    'color' => 'primary',
                    'url' => 'reports/gcash_payments.php'
                ],
                [
                    'title' => 'Daily Sales - Detailed',
                    'icon' => 'chart-bar',
                    'color' => 'success',
                    'url' => '../reports/daily_sales_detailed.php'
                ],
                [
                    'title' => 'Receivable List', 
                    'icon' => 'file-invoice',
                    'color' => 'warning',
                    'url' => '../reports/receivable_list.php'
                ]
            ];

            foreach ($reports as $report): ?>
                <div class="col-md-4">
                    <div class="card report-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-<?= $report['icon'] ?> icon-large text-<?= $report['color'] ?>"></i>
                            <h5 class="card-title"><?= $report['title'] ?></h5>
                            <a href="<?= $report['url'] ?>" class="btn btn-custom btn-<?= $report['color'] ?>">
                                Generate Report
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
