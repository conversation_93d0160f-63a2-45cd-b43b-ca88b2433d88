<?php
/**
 * Test script to verify product validation and subtotal calculation
 */

include_once 'config/database.php';
include_once 'includes/validation.php';

echo "<!DOCTYPE html><html><head><title>Product Validation Test</title></head><body>";
echo "<h1>Product Validation Test</h1>";

try {
    $validator = new TransactionValidator($conn);
    
    // Get a sample product from the database
    $sql = "SELECT * FROM products WHERE stock_quantity > 0 LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $sample_product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sample_product) {
        echo "<h2>Testing with Product:</h2>";
        echo "<ul>";
        echo "<li>Product ID: " . $sample_product['product_id'] . "</li>";
        echo "<li>Name: " . htmlspecialchars($sample_product['name']) . "</li>";
        echo "<li>Price: ₱" . number_format($sample_product['price'], 2) . "</li>";
        echo "<li>Stock: " . $sample_product['stock_quantity'] . "</li>";
        echo "</ul>";
        
        // Test validation
        $test_data = [
            'product_id' => $sample_product['product_id'],
            'quantity' => 2
        ];
        
        echo "<h2>Testing Validation:</h2>";
        $validation_result = $validator->validateAddonProduct($test_data);
        
        if ($validation_result['valid']) {
            echo "<p>✅ Validation passed!</p>";
            
            $product = $validation_result['product'];
            echo "<h3>Product data returned by validation:</h3>";
            echo "<ul>";
            foreach ($product as $key => $value) {
                echo "<li>$key: $value</li>";
            }
            echo "</ul>";
            
            // Test subtotal calculation
            $quantity = $test_data['quantity'];
            if (isset($product['price'])) {
                $subtotal = $quantity * $product['price'];
                echo "<h3>Subtotal Calculation Test:</h3>";
                echo "<p>Quantity: $quantity</p>";
                echo "<p>Price: ₱" . number_format($product['price'], 2) . "</p>";
                echo "<p><strong>Subtotal: ₱" . number_format($subtotal, 2) . "</strong></p>";
                echo "<p>✅ <strong>The line <code>\$subtotal = \$quantity * \$product['price'];</code> is working correctly!</strong></p>";
            } else {
                echo "<p>❌ Price field is missing from product data!</p>";
            }
            
        } else {
            echo "<p>❌ Validation failed:</p>";
            echo "<ul>";
            foreach ($validation_result['errors'] as $error) {
                echo "<li>$error</li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "<p>❌ No products found in database. Please add some products first.</p>";
    }
    
    // Test with invalid data
    echo "<h2>Testing with Invalid Data:</h2>";
    $invalid_data = [
        'product_id' => 99999, // Non-existent product
        'quantity' => 1
    ];
    
    $invalid_result = $validator->validateAddonProduct($invalid_data);
    if (!$invalid_result['valid']) {
        echo "<p>✅ Correctly rejected invalid product:</p>";
        echo "<ul>";
        foreach ($invalid_result['errors'] as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ Should have rejected invalid product!</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='pages/transaction_details.php'>← Back to Transaction Details</a></p>";
echo "</body></html>";
?>
